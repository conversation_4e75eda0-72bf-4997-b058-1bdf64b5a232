/* CSS for public pages */

:root {
    /* Main theme colors - Teal/Green palette */
    --primary-color: #009688;
    --primary-dark: #00796b;
    --primary-light: #b2dfdb;
    --secondary-color: #ff5722;
    --secondary-dark: #e64a19;
    --secondary-light: #ffccbc;
    --success-color: #4caf50;
    --success-dark: #388e3c;
    --success-light: #c8e6c9;
    --info-color: #03a9f4;
    --info-dark: #0288d1;
    --info-light: #b3e5fc;
    --warning-color: #ff9800;
    --warning-dark: #f57c00;
    --warning-light: #ffe0b2;
    --danger-color: #f44336;
    --danger-dark: #d32f2f;
    --danger-light: #ffcdd2;

    /* Neutral colors */
    --gray-50: #fafafa;
    --gray-100: #f5f5f5;
    --gray-200: #eeeeee;
    --gray-300: #e0e0e0;
    --gray-400: #bdbdbd;
    --gray-500: #9e9e9e;
    --gray-600: #757575;
    --gray-700: #616161;
    --gray-800: #424242;
    --gray-900: #212121;

    /* Typography */
    --font-family: 'Tajawal', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-md: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Borders */
    --border-radius-sm: 0.25rem;
    --border-radius: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-full: 9999px;

    /* Transitions */
    --transition-fast: all 0.2s ease;
    --transition: all 0.3s ease;
    --transition-slow: all 0.5s ease;

    /* RGB values for opacity variations */
    --primary-color-rgb: 0, 150, 136;
    --secondary-color-rgb: 255, 87, 34;
    --success-color-rgb: 76, 175, 80;
    --info-color-rgb: 3, 169, 244;
    --warning-color-rgb: 255, 152, 0;
    --danger-color-rgb: 244, 67, 54;
}

/* Global Styles */
html, body {
    height: 100%;
}

body {
    direction: rtl;
    text-align: right;
    font-family: var(--font-family);
    background-color: var(--gray-100);
    color: var(--gray-800);
    line-height: 1.6;
    font-size: var(--font-size-md);
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--gray-900);
    line-height: 1.3;
}

h1 {
    font-size: var(--font-size-3xl);
}

h2 {
    font-size: var(--font-size-2xl);
}

h3 {
    font-size: var(--font-size-xl);
}

h4 {
    font-size: var(--font-size-lg);
}

h5 {
    font-size: var(--font-size-md);
}

h6 {
    font-size: var(--font-size-sm);
}

p {
    margin-bottom: var(--spacing-md);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-dark);
    text-decoration: none;
}

/* Navbar - Enhanced with Effects */
.navbar {
    background: linear-gradient(to bottom, var(--primary-dark), var(--primary-color));
    box-shadow: var(--shadow-md);
    padding: var(--spacing-md) 0;
    color: white;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar-brand {
    font-weight: 700;
    font-size: var(--font-size-xl);
    color: white;
    position: relative;
    padding-bottom: 3px;
    transition: all 0.3s ease;
}

.navbar-brand::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--secondary-color);
    transition: width 0.3s ease;
}

.navbar-brand:hover {
    transform: translateY(-2px);
}

.navbar-brand:hover::after {
    width: 100%;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.85);
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    position: relative;
    margin: 0 2px;
}

.navbar-dark .navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: var(--secondary-color);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-dark .navbar-nav .nav-link:hover::after,
.navbar-dark .navbar-nav .nav-link.active::after {
    width: 80%;
}

.navbar-dark .navbar-nav .nav-link:hover,
.navbar-dark .navbar-nav .nav-link.active {
    color: white;
    transform: translateY(-2px);
}

/* Main content */
.main-content {
    min-height: calc(100vh - 160px);
    padding: var(--spacing-xl) 0;
}

/* Hero section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: var(--spacing-2xl) 0;
    margin-bottom: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="none"><path d="M0,0 L100,0 L100,100 L0,100 Z" fill="rgba(255,255,255,0.05)"/></svg>');
    opacity: 0.5;
}

.hero-section .display-4 {
    font-weight: 700;
    margin-bottom: var(--spacing-md);
}

.hero-section .lead {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-lg);
}

/* Cards - Enhanced with Attractive Borders */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    margin-bottom: var(--spacing-xl);
    transition: all 0.4s ease;
    overflow: hidden;
    background-color: white;
    position: relative;
    border-top: 4px solid transparent;
    border-image: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    border-image-slice: 1;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(var(--primary-color-rgb), 0.1);
    pointer-events: none;
}

.card:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.card-header {
    background-color: white;
    border-bottom: 1px solid var(--gray-200);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h6, .card-header h4 {
    font-weight: 700;
    font-size: var(--font-size-md);
    color: var(--primary-color);
    margin: 0;
}

.card-header.bg-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
    color: white;
}

.card-header.bg-primary h4, .card-header.bg-primary h6 {
    color: white;
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    background-color: white;
    border-top: 1px solid var(--gray-200);
    padding: var(--spacing-md) var(--spacing-lg);
}

/* Card with icon circle */
.rounded-circle.bg-primary {
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.2);
}

.rounded-circle.bg-success {
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(var(--success-color-rgb), 0.2);
}

.card:hover .rounded-circle {
    transform: scale(1.1) rotate(5deg);
}

/* Shadow hover effect */
.shadow-hover {
    transition: all 0.3s ease;
}

.shadow-hover:hover {
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1) !important;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    font-weight: 500;
    transition: var(--transition-fast);
    text-transform: none;
    letter-spacing: normal;
    position: relative;
    overflow: hidden;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.5s, height 0.5s;
    z-index: 1;
    pointer-events: none;
}

.btn:active::after {
    width: 300%;
    height: 300%;
}

.btn:focus {
    box-shadow: 0 0 0 0.25rem rgba(var(--primary-color-rgb), 0.25);
}

.btn i {
    margin-left: var(--spacing-sm);
    font-size: 1.1em;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.btn-outline-light {
    color: white;
    border-color: white;
}

.btn-outline-light:hover {
    background-color: white;
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-lg);
    border-radius: var(--border-radius-lg);
}

/* Forms */
.form-control {
    border-radius: var(--border-radius-md);
    border: 1px solid var(--gray-300);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-md);
    transition: var(--transition-fast);
    color: var(--gray-800);
    background-color: white;
    height: auto;
    line-height: 1.5;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(var(--primary-color-rgb), 0.25);
    background-color: white;
}

.form-label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    display: block;
}

.form-text {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    display: block;
}

/* Alerts */
.alert {
    border-radius: var(--border-radius-lg);
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
}

.alert-success {
    background-color: var(--success-light);
    color: var(--success-dark);
}

.alert-success::before {
    background-color: var(--success-color);
}

.alert-info {
    background-color: var(--info-light);
    color: var(--info-dark);
}

.alert-info::before {
    background-color: var(--info-color);
}

.alert-warning {
    background-color: var(--warning-light);
    color: var(--warning-dark);
}

.alert-warning::before {
    background-color: var(--warning-color);
}

.alert-danger {
    background-color: var(--danger-light);
    color: var(--danger-dark);
}

.alert-danger::before {
    background-color: var(--danger-color);
}

.alert i {
    margin-left: var(--spacing-sm);
    font-size: 1.1em;
}

/* Footer */
.footer {
    background: linear-gradient(to bottom, var(--primary-color), var(--primary-dark));
    color: white;
    padding: var(--spacing-lg) 0;
    margin-top: var(--spacing-2xl);
}

/* Enhanced Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

.float {
    animation: float 3s ease-in-out infinite;
}

/* Navbar icon animations */
.navbar-dark .navbar-nav .nav-link i {
    transition: all 0.3s ease;
}

.navbar-dark .navbar-nav .nav-link:hover i {
    transform: translateY(-3px);
}

/* Add Tajawal font */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap');
