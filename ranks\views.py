from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.db.models import Q
from django.utils import timezone
import pandas as pd
from datetime import datetime
from io import BytesIO
import xlsxwriter
import arabic_reshaper
from bidi.algorithm import get_display
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4, landscape
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

from .models import RankType, EmployeeRank
from .forms import RankTypeForm, EmployeeRankForm, EmployeeRankImportForm
from employees.models import Employee
from employment.models import EmployeeAllowance
from employment.forms import EmployeeAllowanceForm, EmployeeAllowanceSearchForm

# Rank Types Views
@login_required
def rank_type_list(request):
    """View for listing and creating rank types"""
    rank_types = RankType.objects.all()

    if request.method == 'POST':
        form = RankTypeForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة نوع الرتبة بنجاح.')
            return redirect('ranks:rank_type_list')
    else:
        form = RankTypeForm()

    return render(request, 'ranks/rank_type_list.html', {
        'rank_types': rank_types,
        'form': form
    })

@login_required
def rank_type_update(request, pk):
    """View for updating a rank type"""
    rank_type = get_object_or_404(RankType, pk=pk)

    if request.method == 'POST':
        form = RankTypeForm(request.POST, instance=rank_type)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث نوع الرتبة بنجاح.')
            return redirect('ranks:rank_type_list')
    else:
        form = RankTypeForm(instance=rank_type)

    return JsonResponse({
        'id': rank_type.id,
        'name': rank_type.name,
        'description': rank_type.description or ''
    })

@login_required
def rank_type_delete(request, pk):
    """View for deleting a rank type"""
    rank_type = get_object_or_404(RankType, pk=pk)
    rank_type.delete()
    messages.success(request, 'تم حذف نوع الرتبة بنجاح.')
    return redirect('ranks:rank_type_list')

# Employee Ranks Views
@login_required
def employee_rank_list(request):
    """View for listing employee ranks and exporting to Excel/PDF"""
    search_query = request.GET.get('search', '')
    export_format = request.GET.get('export', '')
    selected_year = request.GET.get('year', '')
    selected_rank_type = request.GET.get('rank_type', '')

    # Get all employee ranks
    employee_ranks = EmployeeRank.objects.all()

    # Apply search filter if provided
    if search_query:
        employee_ranks = employee_ranks.filter(
            Q(employee__full_name__icontains=search_query) |
            Q(employee__ministry_number__icontains=search_query) |
            Q(rank_type__name__icontains=search_query)
        )

    # Apply year filter if provided
    if selected_year and selected_year != 'all':
        employee_ranks = employee_ranks.filter(date_obtained__year=selected_year)

    # Apply rank type filter if provided
    if selected_rank_type and selected_rank_type != 'all':
        employee_ranks = employee_ranks.filter(rank_type_id=selected_rank_type)

    # Get available years from the data
    years = EmployeeRank.objects.dates('date_obtained', 'year').values_list('date_obtained__year', flat=True)
    years = sorted(set(years), reverse=True)

    # Get all rank types
    rank_types = RankType.objects.all()

    # Handle export requests
    if export_format == 'excel':
        return export_ranks_to_excel(employee_ranks)
    elif export_format == 'pdf_preview':
        return render(request, 'ranks/employee_rank_pdf_preview.html', {
            'employee_ranks': employee_ranks,
            'search_query': search_query,
            'selected_year': selected_year,
            'selected_rank_type': selected_rank_type,
            'employee_count': employee_ranks.count()
        })

    return render(request, 'ranks/employee_rank_list.html', {
        'employee_ranks': employee_ranks,
        'search_query': search_query,
        'years': years,
        'rank_types': rank_types,
        'selected_year': selected_year,
        'selected_rank_type': selected_rank_type,
        'employee_count': employee_ranks.count()
    })

def export_ranks_to_excel(employee_ranks):
    """Export employee ranks to Excel"""
    # Create a BytesIO buffer
    output = BytesIO()

    # Create a workbook and add a worksheet
    workbook = xlsxwriter.Workbook(output)
    worksheet = workbook.add_worksheet('رتب الموظفين')

    # Add formats
    header_format = workbook.add_format({
        'bold': True,
        'align': 'center',
        'valign': 'vcenter',
        'bg_color': '#f2f2f2',
        'border': 1,
        'font_size': 12
    })

    cell_format = workbook.add_format({
        'align': 'center',
        'valign': 'vcenter',
        'border': 1,
        'font_size': 11
    })

    title_format = workbook.add_format({
        'bold': True,
        'align': 'center',
        'valign': 'vcenter',
        'font_size': 14
    })

    # Add title and metadata
    worksheet.merge_range('A1:E1', 'تقرير رتب الموظفين', title_format)
    worksheet.merge_range('A2:E2', f'عدد الموظفين: {employee_ranks.count()}', title_format)
    worksheet.merge_range('A3:E3', f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d")}', title_format)

    # Set column widths
    worksheet.set_column('A:A', 15)  # Ministry Number
    worksheet.set_column('B:B', 30)  # Employee Name
    worksheet.set_column('C:C', 20)  # Rank Type
    worksheet.set_column('D:D', 15)  # Date Obtained
    worksheet.set_column('E:E', 30)  # Notes

    # Write headers
    headers = ['الرقم الوزاري', 'اسم الموظف', 'نوع الرتبة', 'تاريخ الحصول عليها', 'ملاحظات']
    for col_num, header in enumerate(headers):
        worksheet.write(4, col_num, header, header_format)

    # Write data
    for row_num, rank in enumerate(employee_ranks, 5):
        worksheet.write(row_num, 0, rank.employee.ministry_number, cell_format)
        worksheet.write(row_num, 1, rank.employee.full_name, cell_format)
        worksheet.write(row_num, 2, rank.rank_type.name, cell_format)
        worksheet.write(row_num, 3, rank.date_obtained.strftime('%Y-%m-%d'), cell_format)
        worksheet.write(row_num, 4, rank.notes or '', cell_format)

    # Close the workbook
    workbook.close()

    # Create response
    output.seek(0)
    response = HttpResponse(output.read(), content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename=employee_ranks.xlsx'

    return response

def export_ranks_to_pdf(employee_ranks):
    """Export employee ranks to PDF"""
    # Register Arabic font
    try:
        pdfmetrics.registerFont(TTFont('NotoSansArabic', 'static/fonts/NotoSansArabic-Regular.ttf'))
    except:
        pass  # Font already registered or not found

    # Create a BytesIO buffer
    buffer = BytesIO()

    # Create the PDF document
    doc = SimpleDocTemplate(
        buffer,
        pagesize=landscape(A4),
        rightMargin=25,
        leftMargin=25,
        topMargin=25,
        bottomMargin=25
    )

    # Container for the 'Flowable' objects
    elements = []

    # Define styles
    styles = getSampleStyleSheet()

    # Create Arabic paragraph style
    arabic_style = ParagraphStyle(
        'ArabicStyle',
        parent=styles['Heading1'],
        fontName='NotoSansArabic',
        alignment=1,  # Center alignment
        fontSize=16,
        leading=20
    )

    # Add title
    try:
        title_text = "رتب الموظفين"
        reshaped_title = arabic_reshaper.reshape(title_text)
        bidi_title = get_display(reshaped_title)
        title = Paragraph(bidi_title, arabic_style)
        elements.append(title)
    except:
        title = Paragraph("Employee Ranks", styles['Heading1'])
        elements.append(title)

    elements.append(Spacer(1, 20))

    # Create table data
    data = []

    # Add headers
    headers = ['ملاحظات', 'تاريخ الحصول عليها', 'نوع الرتبة', 'اسم الموظف', 'الرقم الوزاري']
    data.append(headers)

    # Add data rows
    for rank in employee_ranks:
        row = [
            rank.notes or '',
            rank.date_obtained.strftime('%Y-%m-%d'),
            rank.rank_type.name,
            rank.employee.full_name,
            rank.employee.ministry_number
        ]
        data.append(row)

    # Create the table
    col_widths = [200, 100, 120, 200, 100]  # Adjust column widths as needed
    table = Table(data, colWidths=col_widths, repeatRows=1)

    # Style the table
    table_style = TableStyle([
        # Basic styling
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'NotoSansArabic'),  # Header font
        ('FONTSIZE', (0, 0), (-1, 0), 10),  # Header font size
        ('BACKGROUND', (0, 0), (-1, 0), colors.beige),  # Header background
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),  # Header text color
        ('GRID', (0, 0), (-1, -1), 1, colors.black),  # Grid lines
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),  # Vertical alignment

        # Data rows styling
        ('FONTNAME', (0, 1), (-1, -1), 'NotoSansArabic'),  # Data font
        ('FONTSIZE', (0, 1), (-1, -1), 8),  # Data font size
        ('BACKGROUND', (0, 1), (-1, -1), colors.white),  # Data background
    ])

    # Apply alternating row colors
    for row in range(1, len(data)):
        if row % 2 == 0:
            table_style.add('BACKGROUND', (0, row), (-1, row), colors.Color(0.95, 0.95, 0.95))

    table.setStyle(table_style)
    elements.append(table)

    # Add page number and date function
    def add_page_number_and_date(canvas, doc):
        # Save the state of our canvas so we can draw on it
        canvas.saveState()

        # Get current date and format it
        current_date = timezone.now().strftime("%d/%m/%Y")

        # Add date at the bottom left
        try:
            date_text = f"تاريخ التقرير: {current_date}"
            reshaped_date = arabic_reshaper.reshape(date_text)
            bidi_date = get_display(reshaped_date)
            canvas.setFont('NotoSansArabic', 6)
            canvas.drawString(5, 5, bidi_date)
        except:
            canvas.setFont('Helvetica', 6)
            canvas.drawString(5, 5, f"Report Date: {current_date}")

        # Add page number at the bottom right
        page_num = canvas.getPageNumber()
        canvas.drawRightString(landscape(A4)[0] - 5, 5, str(page_num))

        # Release the canvas
        canvas.restoreState()

    # Build the PDF document
    doc.build(elements, onFirstPage=add_page_number_and_date, onLaterPages=add_page_number_and_date)

    # Get the value of the BytesIO buffer
    pdf = buffer.getvalue()
    buffer.close()

    # Create the HTTP response
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename="employee_ranks.pdf"'
    response.write(pdf)

    return response

@login_required
def employee_rank_create(request):
    """View for creating a new employee rank"""
    if request.method == 'POST':
        form = EmployeeRankForm(request.POST)
        if form.is_valid():
            try:
                # Get employee from employee_id
                employee_id = form.cleaned_data.get('employee_id')
                ministry_number = form.cleaned_data.get('ministry_number')

                # Find employee
                employee = None
                if employee_id:
                    try:
                        employee = Employee.objects.get(id=employee_id)
                    except Employee.DoesNotExist:
                        messages.error(request, 'لم يتم العثور على الموظف.')
                        return render(request, 'ranks/employee_rank_form.html', {'form': form})
                elif ministry_number:
                    try:
                        employee = Employee.objects.get(ministry_number=ministry_number)
                    except Employee.DoesNotExist:
                        messages.error(request, 'لم يتم العثور على موظف بهذا الرقم الوزاري')
                        return render(request, 'ranks/employee_rank_form.html', {'form': form})
                else:
                    messages.error(request, 'الرجاء إدخال الرقم الوزاري للموظف')
                    return render(request, 'ranks/employee_rank_form.html', {'form': form})

                # Get form data
                rank_type = form.cleaned_data['rank_type']
                date_obtained = form.cleaned_data['date_obtained']
                notes = form.cleaned_data.get('notes', '')

                # Create employee rank
                employee_rank = EmployeeRank(
                    employee=employee,
                    rank_type=rank_type,
                    date_obtained=date_obtained,
                    notes=notes
                )
                employee_rank.save()

                messages.success(request, 'تم إضافة الرتبة للموظف بنجاح.')
                return redirect('ranks:employee_rank_list')

            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء حفظ الرتبة: {str(e)}')
        else:
            print('Form is invalid. Errors:', form.errors)
    else:
        form = EmployeeRankForm()

    return render(request, 'ranks/employee_rank_form.html', {'form': form})

@login_required
def employee_rank_detail(request, pk):
    """View for showing employee rank details"""
    employee_rank = get_object_or_404(EmployeeRank, pk=pk)
    return render(request, 'ranks/employee_rank_detail.html', {'employee_rank': employee_rank})

@login_required
def employee_rank_update(request, pk):
    """View for updating an employee rank"""
    employee_rank = get_object_or_404(EmployeeRank, pk=pk)

    if request.method == 'POST':
        form = EmployeeRankForm(request.POST)
        if form.is_valid():
            try:
                # Get form data
                rank_type = form.cleaned_data['rank_type']
                date_obtained = form.cleaned_data['date_obtained']
                notes = form.cleaned_data.get('notes', '')

                # Update employee rank
                employee_rank.rank_type = rank_type
                employee_rank.date_obtained = date_obtained
                employee_rank.notes = notes
                employee_rank.save()

                messages.success(request, 'تم تحديث الرتبة بنجاح.')
                return redirect('ranks:employee_rank_list')

            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء تحديث الرتبة: {str(e)}')
    else:
        # Initialize form with current values
        initial_data = {
            'employee_id': employee_rank.employee.id,
            'ministry_number': employee_rank.employee.ministry_number,
            'employee_name': employee_rank.employee.full_name,
            'rank_type': employee_rank.rank_type,
            'date_obtained': employee_rank.date_obtained,
            'notes': employee_rank.notes
        }
        form = EmployeeRankForm(initial=initial_data)

    return render(request, 'ranks/employee_rank_form.html', {
        'form': form,
        'employee_rank': employee_rank
    })

@login_required
def employee_rank_delete(request, pk):
    """View for deleting an employee rank"""
    employee_rank = get_object_or_404(EmployeeRank, pk=pk)
    employee_rank.delete()
    messages.success(request, 'تم حذف الرتبة بنجاح.')
    return redirect('ranks:employee_rank_list')

@login_required
def employee_rank_import(request):
    """View for importing employee ranks from Excel file"""
    if request.method == 'POST':
        form = EmployeeRankImportForm(request.POST, request.FILES)
        if form.is_valid():
            excel_file = request.FILES['excel_file']
            default_rank_type = form.cleaned_data.get('rank_type_mapping')

            # Check file extension
            if not excel_file.name.endswith(('.xls', '.xlsx')):
                messages.error(request, 'الرجاء تحميل ملف Excel صالح (.xls, .xlsx)')
                return render(request, 'ranks/employee_rank_import.html', {'form': form})

            try:
                # Read Excel file
                df = pd.read_excel(excel_file)

                # Check required columns
                required_columns = ['الرقم الوزاري']
                missing_columns = [col for col in required_columns if col not in df.columns]

                if missing_columns:
                    messages.error(request, f'الأعمدة التالية مفقودة من الملف: {", ".join(missing_columns)}')
                    return render(request, 'ranks/employee_rank_import.html', {'form': form})

                # Process data
                success_count = 0
                error_count = 0
                error_messages = []

                for index, row in df.iterrows():
                    try:
                        # Get ministry number
                        ministry_number = str(row.get('الرقم الوزاري', ''))
                        if not ministry_number:
                            error_count += 1
                            error_messages.append(f'الصف {index+2}: الرقم الوزاري مفقود')
                            continue

                        # Find employee
                        try:
                            employee = Employee.objects.get(ministry_number=ministry_number)
                        except Employee.DoesNotExist:
                            error_count += 1
                            error_messages.append(f'الصف {index+2}: لم يتم العثور على موظف بالرقم الوزاري {ministry_number}')
                            continue

                        # Get rank type
                        rank_type_name = row.get('نوع الرتبة', '')
                        if rank_type_name:
                            try:
                                rank_type = RankType.objects.get(name=rank_type_name)
                            except RankType.DoesNotExist:
                                if default_rank_type:
                                    rank_type = default_rank_type
                                else:
                                    error_count += 1
                                    error_messages.append(f'الصف {index+2}: لم يتم العثور على نوع الرتبة {rank_type_name}')
                                    continue
                        elif default_rank_type:
                            rank_type = default_rank_type
                        else:
                            error_count += 1
                            error_messages.append(f'الصف {index+2}: نوع الرتبة مفقود ولم يتم تحديد نوع افتراضي')
                            continue

                        # Get date obtained
                        date_str = row.get('تاريخ الحصول عليها', '')
                        if date_str:
                            try:
                                if isinstance(date_str, str):
                                    date_obtained = datetime.strptime(date_str, '%Y-%m-%d').date()
                                else:
                                    date_obtained = date_str.date() if hasattr(date_str, 'date') else date_str
                            except (ValueError, AttributeError):
                                date_obtained = datetime.now().date()
                        else:
                            date_obtained = datetime.now().date()

                        # Get notes
                        notes = row.get('ملاحظات', '')

                        # Create or update employee rank
                        employee_rank, created = EmployeeRank.objects.update_or_create(
                            employee=employee,
                            rank_type=rank_type,
                            defaults={
                                'date_obtained': date_obtained,
                                'notes': notes
                            }
                        )

                        success_count += 1
                    except Exception as e:
                        error_count += 1
                        error_messages.append(f'الصف {index+2}: {str(e)}')

                # Show results
                if success_count > 0:
                    messages.success(request, f'تم استيراد {success_count} رتبة بنجاح.')

                if error_count > 0:
                    messages.warning(request, f'حدثت {error_count} أخطاء أثناء الاستيراد.')
                    for error in error_messages[:10]:  # Show first 10 errors
                        messages.error(request, error)

                    if len(error_messages) > 10:
                        messages.error(request, f'و {len(error_messages) - 10} أخطاء أخرى...')

                return redirect('ranks:employee_rank_list')
            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء معالجة الملف: {str(e)}')
    else:
        form = EmployeeRankImportForm()

    # Create template for download
    if 'download_template' in request.GET:
        # Create a sample dataframe
        data = {
            'الرقم الوزاري': ['123456', '789012'],
            'نوع الرتبة': ['مدير', 'مشرف'],
            'تاريخ الحصول عليها': ['2023-01-01', '2023-02-15'],
            'ملاحظات': ['ملاحظة 1', 'ملاحظة 2']
        }
        df = pd.DataFrame(data)

        # Create Excel file
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='رتب الموظفين')

        # Create response
        output.seek(0)
        response = HttpResponse(output.read(), content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename=employee_ranks_template.xlsx'
        return response

    return render(request, 'ranks/employee_rank_import.html', {'form': form})


# Employee Allowances Views
@login_required
def employee_allowance_list(request):
    """View for listing employee allowances"""
    # Get search parameters
    search_term = request.GET.get('search', '')
    education_allowance = request.GET.get('education_allowance', '')
    adjustment_allowance = request.GET.get('adjustment_allowance', '')
    transportation_allowance = request.GET.get('transportation_allowance', '')
    supervisory_allowance = request.GET.get('supervisory_allowance', '')
    technical_allowance = request.GET.get('technical_allowance', '')

    # Start with all allowances
    allowances = EmployeeAllowance.objects.select_related('employee').all()

    # Apply filters
    if search_term:
        allowances = allowances.filter(
            Q(employee__full_name__icontains=search_term) |
            Q(employee__ministry_number__icontains=search_term)
        )

    if education_allowance:
        allowances = allowances.filter(education_allowance=education_allowance)

    if adjustment_allowance:
        allowances = allowances.filter(adjustment_allowance=adjustment_allowance)

    if transportation_allowance:
        allowances = allowances.filter(transportation_allowance=transportation_allowance)

    if supervisory_allowance:
        allowances = allowances.filter(supervisory_allowance=supervisory_allowance)

    if technical_allowance:
        allowances = allowances.filter(technical_allowance=technical_allowance)

    # Order by employee name
    allowances = allowances.order_by('employee__full_name')

    # Create search form
    search_form = EmployeeAllowanceSearchForm(request.GET)

    # Calculate statistics
    total_allowances = allowances.count()
    education_count = allowances.filter(education_allowance='yes').count()
    adjustment_count = allowances.filter(adjustment_allowance='yes').count()
    transportation_count = allowances.filter(transportation_allowance='yes').count()
    supervisory_count = allowances.filter(supervisory_allowance='yes').count()
    technical_count = allowances.filter(technical_allowance='yes').count()

    # Handle Excel export
    if request.GET.get('export') == 'excel':
        return export_allowances_to_excel(allowances)

    return render(request, 'ranks/employee_allowance_list.html', {
        'allowances': allowances,
        'search_form': search_form,
        'search_term': search_term,
        'education_allowance': education_allowance,
        'adjustment_allowance': adjustment_allowance,
        'transportation_allowance': transportation_allowance,
        'supervisory_allowance': supervisory_allowance,
        'technical_allowance': technical_allowance,
        'total_allowances': total_allowances,
        'education_count': education_count,
        'adjustment_count': adjustment_count,
        'transportation_count': transportation_count,
        'supervisory_count': supervisory_count,
        'technical_count': technical_count,
    })


@login_required
def employee_allowance_create(request):
    """View for creating a new employee allowance"""
    if request.method == 'POST':
        form = EmployeeAllowanceForm(request.POST)
        if form.is_valid():
            allowance = form.save()
            messages.success(request, f'تم إضافة علاوات الموظف "{allowance.employee.full_name}" بنجاح.')
            return redirect('ranks:employee_allowance_list')
    else:
        form = EmployeeAllowanceForm()

    return render(request, 'ranks/employee_allowance_form.html', {
        'form': form,
        'title': 'إضافة علاوات موظف جديد'
    })


@login_required
def employee_allowance_update(request, pk):
    """View for updating an employee allowance"""
    allowance = get_object_or_404(EmployeeAllowance, pk=pk)

    if request.method == 'POST':
        form = EmployeeAllowanceForm(request.POST, instance=allowance)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث علاوات الموظف "{allowance.employee.full_name}" بنجاح.')
            return redirect('ranks:employee_allowance_list')
    else:
        form = EmployeeAllowanceForm(instance=allowance)

    return render(request, 'ranks/employee_allowance_form.html', {
        'form': form,
        'allowance': allowance,
        'title': 'تحديث علاوات الموظف'
    })


@login_required
def employee_allowance_delete(request, pk):
    """View for deleting an employee allowance"""
    allowance = get_object_or_404(EmployeeAllowance, pk=pk)

    if request.method == 'POST':
        employee_name = allowance.employee.full_name
        allowance.delete()
        messages.success(request, f'تم حذف علاوات الموظف "{employee_name}" بنجاح.')
        return redirect('ranks:employee_allowance_list')

    return render(request, 'ranks/employee_allowance_confirm_delete.html', {
        'allowance': allowance
    })


def export_allowances_to_excel(allowances):
    """Export employee allowances to Excel"""
    # Create a BytesIO buffer
    output = BytesIO()

    # Create a workbook and add a worksheet
    workbook = xlsxwriter.Workbook(output)
    worksheet = workbook.add_worksheet('علاوات الموظفين')

    # Add formats
    header_format = workbook.add_format({
        'bold': True,
        'align': 'center',
        'valign': 'vcenter',
        'bg_color': '#f2f2f2',
        'border': 1,
        'font_size': 12
    })

    cell_format = workbook.add_format({
        'align': 'center',
        'valign': 'vcenter',
        'border': 1,
        'font_size': 11
    })

    title_format = workbook.add_format({
        'bold': True,
        'align': 'center',
        'valign': 'vcenter',
        'font_size': 14
    })

    # Add title and metadata
    worksheet.merge_range('A1:H1', 'تقرير علاوات الموظفين', title_format)
    worksheet.merge_range('A2:H2', f'عدد الموظفين: {allowances.count()}', title_format)
    worksheet.merge_range('A3:H3', f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d")}', title_format)

    # Set column widths
    worksheet.set_column('A:A', 8)   # الرقم
    worksheet.set_column('B:B', 15)  # الرقم الوزاري
    worksheet.set_column('C:C', 25)  # الاسم الكامل
    worksheet.set_column('D:D', 12)  # علاوة التعليم
    worksheet.set_column('E:E', 12)  # التجيير
    worksheet.set_column('F:F', 12)  # التنقلات
    worksheet.set_column('G:G', 15)  # العلاوة الإشرافية
    worksheet.set_column('H:H', 12)  # علاوة فنية

    # Write headers
    headers = ['الرقم', 'الرقم الوزاري', 'الاسم الكامل', 'علاوة التعليم', 'التجيير', 'التنقلات', 'العلاوة الإشرافية', 'علاوة فنية']
    for col_num, header in enumerate(headers):
        worksheet.write(4, col_num, header, header_format)

    # Write data
    for row_num, allowance in enumerate(allowances, 5):
        # Get ministry number from employee identification
        ministry_number = allowance.ministry_number

        data = [
            row_num - 4,  # Serial number
            ministry_number,
            allowance.employee.full_name,
            'نعم' if allowance.education_allowance == 'yes' else 'لا',
            'نعم' if allowance.adjustment_allowance == 'yes' else 'لا',
            'نعم' if allowance.transportation_allowance == 'yes' else 'لا',
            'نعم' if allowance.supervisory_allowance == 'yes' else 'لا',
            'نعم' if allowance.technical_allowance == 'yes' else 'لا',
        ]

        for col_num, value in enumerate(data):
            worksheet.write(row_num, col_num, value, cell_format)

    # Close the workbook
    workbook.close()

    # Create response
    output.seek(0)
    response = HttpResponse(output.read(), content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename=employee_allowances.xlsx'

    return response