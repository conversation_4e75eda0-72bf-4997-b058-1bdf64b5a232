from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q
from django.utils import timezone
from django.http import JsonResponse
from .models import LeaveT<PERSON>, LeaveBalance, Leave, Departure
from .forms import LeaveTypeForm, LeaveBalanceForm, LeaveForm, DepartureForm
from employees.models import Employee

@login_required
def leave_list(request):
    search_query = request.GET.get('search', '')
    if search_query:
        leaves = Leave.objects.filter(
            Q(employee__full_name__icontains=search_query) |
            Q(employee__ministry_number__icontains=search_query) |
            Q(reason__icontains=search_query)
        )
    else:
        leaves = Leave.objects.all()

    return render(request, 'leaves/leave_list.html', {
        'leaves': leaves,
        'search_query': search_query
    })

@login_required
def leave_create(request):
    if request.method == 'POST':
        print('POST data:', request.POST)

        # Create a copy of POST data to modify
        post_data = request.POST.copy()

        # Get employee_id from POST data
        employee_id = post_data.get('employee_id')
        ministry_number = post_data.get('ministry_number')

        # If we have ministry_number but no employee_id, try to get employee_id
        if ministry_number and not employee_id:
            try:
                employee = Employee.objects.get(ministry_number=ministry_number)
                post_data['employee'] = str(employee.id)
                print(f'Found employee by ministry number: {employee.id} - {employee.full_name}')
            except Employee.DoesNotExist:
                messages.error(request, 'لم يتم العثور على موظف بهذا الرقم الوزاري')
                return render(request, 'leaves/leave_form.html', {'form': LeaveForm(post_data)})

        # If we have employee_id but no employee, set employee
        if employee_id and not post_data.get('employee'):
            try:
                employee = Employee.objects.get(id=employee_id)
                post_data['employee'] = str(employee.id)
                print(f'Found employee by ID: {employee.id} - {employee.full_name}')
            except Employee.DoesNotExist:
                messages.error(request, 'لم يتم العثور على موظف بهذا الرقم')
                return render(request, 'leaves/leave_form.html', {'form': LeaveForm(post_data)})

        # Create form with modified POST data
        form = LeaveForm(post_data)

        print('Modified POST data:', post_data)

        if form.is_valid():
            try:
                # Get the employee
                employee_id = post_data.get('employee')
                if not employee_id:
                    employee_id = post_data.get('employee_id')

                if not employee_id:
                    messages.error(request, 'الرجاء اختيار موظف')
                    return render(request, 'leaves/leave_form.html', {'form': form})

                try:
                    employee = Employee.objects.get(id=employee_id)
                except Employee.DoesNotExist:
                    messages.error(request, 'لم يتم العثور على موظف بهذا الرقم')
                    return render(request, 'leaves/leave_form.html', {'form': form})

                # Create a new leave
                leave = form.save(commit=False)
                leave.employee = employee

                # Calculate days count if not provided
                if not leave.days_count:
                    delta = leave.end_date - leave.start_date
                    leave.days_count = delta.days + 1

                # Set status to 'approved' by default
                leave.status = 'approved'

                # Save the leave
                leave.save()
                print('Leave saved successfully with ID:', leave.id)

                # Update leave balance
                try:
                    balance = LeaveBalance.objects.get(
                        employee=leave.employee,
                        leave_type=leave.leave_type,
                        year=timezone.now().year
                    )
                    balance.used_balance += leave.days_count
                    balance.save()
                    print(f'Updated leave balance: {balance.id}, new used balance: {balance.used_balance}')
                except LeaveBalance.DoesNotExist:
                    print('No leave balance found for this employee, leave type and year')

                messages.success(request, 'تم إضافة الإجازة بنجاح.')

                # Create notification for new leave request
                try:
                    from notifications.utils import notify_leave_request
                    notify_leave_request(leave, request.user)
                except ImportError:
                    pass  # Notifications app not available

                return redirect('leaves:leave_list')

            except Exception as e:
                print('Error saving leave:', str(e))
                messages.error(request, f'حدث خطأ أثناء حفظ الإجازة: {str(e)}')
        else:
            print('Form is invalid. Errors:', form.errors)
            # Display form errors to the user
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{error}')
                print(f'Field {field} errors: {errors}')
    else:
        form = LeaveForm()

    return render(request, 'leaves/leave_form.html', {'form': form})

@login_required
def leave_detail(request, pk):
    leave = get_object_or_404(Leave, pk=pk)
    return render(request, 'leaves/leave_detail.html', {'leave': leave})

@login_required
def leave_update(request, pk):
    leave = get_object_or_404(Leave, pk=pk)
    old_days_count = leave.days_count

    if request.method == 'POST':
        form = LeaveForm(request.POST, instance=leave)
        if form.is_valid():
            updated_leave = form.save(commit=False)
            # Calculate days count if not provided
            if not updated_leave.days_count:
                delta = updated_leave.end_date - updated_leave.start_date
                updated_leave.days_count = delta.days + 1

            # Update leave balance
            try:
                balance = LeaveBalance.objects.get(
                    employee=leave.employee,
                    leave_type=leave.leave_type,
                    year=timezone.now().year
                )
                balance.used_balance = balance.used_balance - old_days_count + updated_leave.days_count
                balance.save()
            except LeaveBalance.DoesNotExist:
                pass

            updated_leave.save()
            messages.success(request, 'تم تحديث بيانات الإجازة بنجاح.')
            return redirect('leaves:leave_detail', pk=leave.pk)
        else:
            # Display form errors
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{error}')
    else:
        # Initialize form with employee data for the template
        form = LeaveForm(instance=leave, initial={
            'ministry_number': leave.employee.ministry_number,
            'employee_name': leave.employee.full_name,
            'employee_id': leave.employee.id
        })
    return render(request, 'leaves/leave_form.html', {'form': form, 'leave': leave})

@login_required
def leave_delete(request, pk):
    leave = get_object_or_404(Leave, pk=pk)
    if request.method == 'POST':
        # Update leave balance
        try:
            balance = LeaveBalance.objects.get(
                employee=leave.employee,
                leave_type=leave.leave_type,
                year=timezone.now().year
            )
            balance.used_balance -= leave.days_count
            balance.save()
        except LeaveBalance.DoesNotExist:
            pass

        leave.delete()
        messages.success(request, 'تم حذف الإجازة بنجاح.')
        return redirect('leaves:leave_list')
    return render(request, 'leaves/leave_confirm_delete.html', {'leave': leave})

@login_required
def leave_type_list(request):
    leave_types = LeaveType.objects.all()
    return render(request, 'leaves/leave_type_list.html', {'leave_types': leave_types})

@login_required
def leave_type_create(request):
    if request.method == 'POST':
        form = LeaveTypeForm(request.POST)
        if form.is_valid():
            leave_type = form.save()
            messages.success(request, 'تم إضافة نوع الإجازة بنجاح.')
            return redirect('leaves:leave_type_list')
    else:
        form = LeaveTypeForm()
    return render(request, 'leaves/leave_type_form.html', {'form': form})

@login_required
def leave_type_update(request, pk):
    leave_type = get_object_or_404(LeaveType, pk=pk)
    if request.method == 'POST':
        form = LeaveTypeForm(request.POST, instance=leave_type)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث نوع الإجازة بنجاح.')
            return redirect('leaves:leave_type_list')
    else:
        form = LeaveTypeForm(instance=leave_type)
    return render(request, 'leaves/leave_type_form.html', {'form': form, 'leave_type': leave_type})

@login_required
def leave_balance_list(request):
    balances = LeaveBalance.objects.all()
    return render(request, 'leaves/leave_balance_list.html', {'balances': balances})

@login_required
def get_employee_by_ministry_number(request):
    """AJAX view to get employee details by ministry number"""
    ministry_number = request.GET.get('ministry_number', '')
    if not ministry_number:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوزاري'})

    try:
        employee = Employee.objects.get(ministry_number=ministry_number)
        return JsonResponse({
            'success': True,
            'employee': {
                'id': employee.id,
                'full_name': employee.full_name,
                'ministry_number': employee.ministry_number
            }
        })
    except Employee.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري'})

@login_required
def get_used_leave_days(request):
    """AJAX view to get used leave days for an employee"""
    employee_id = request.GET.get('employee_id', '')
    leave_type_id = request.GET.get('leave_type_id', '')
    year = request.GET.get('year', '')

    if not all([employee_id, leave_type_id, year]):
        return JsonResponse({'success': False, 'error': 'الرجاء توفير جميع البيانات المطلوبة'})

    try:
        # Get all leaves for this employee, leave type and year
        leaves = Leave.objects.filter(
            employee_id=employee_id,
            leave_type_id=leave_type_id,
            start_date__year=year,
            status='approved'
        )

        # Calculate total used days
        used_days = sum(leave.days_count for leave in leaves)

        return JsonResponse({
            'success': True,
            'used_days': used_days
        })
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
def get_leave_balance_whatsapp(request, employee_id):
    """Get leave balance and last leave date for WhatsApp message"""
    try:
        # Get employee
        employee = Employee.objects.get(pk=employee_id)

        # Get current year
        current_year = timezone.now().year

        # Get all leave balances for this employee for the current year
        balances = LeaveBalance.objects.filter(
            employee_id=employee_id,
            year=current_year
        )

        # Get the latest leave for this employee
        latest_leave = Leave.objects.filter(
            employee_id=employee_id,
            status='approved'
        ).order_by('-end_date').first()

        # Format phone number (remove any non-digit characters and ensure it starts with 962)
        phone = employee.phone_number
        if phone:
            # Remove any non-digit characters
            phone = ''.join(filter(str.isdigit, phone))
            # Ensure it starts with 962 (Jordan country code)
            if phone.startswith('0'):
                phone = '962' + phone[1:]
            elif not phone.startswith('962'):
                phone = '962' + phone
        else:
            return JsonResponse({'success': False, 'error': 'لا يوجد رقم هاتف لهذا الموظف'})

        # Construct message
        message = f"مرحباً {employee.full_name}،\n\nأرصدة الإجازات الخاصة بك لعام {current_year}:\n"

        for balance in balances:
            message += f"- {balance.leave_type.get_name_display()}: {balance.remaining_balance} يوم\n"

        if latest_leave:
            message += f"\nتاريخ آخر إجازة: من {latest_leave.start_date} إلى {latest_leave.end_date}\n"
        else:
            message += "\nلا يوجد إجازات سابقة\n"

        message += "\nمع تحيات قسم شؤون الموظفين"

        # Create WhatsApp URL
        whatsapp_url = f"https://wa.me/{phone}?text={message}"

        return JsonResponse({
            'success': True,
            'whatsapp_url': whatsapp_url,
            'phone': phone,
            'message': message
        })
    except Employee.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على الموظف'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
def leave_balance_create(request):
    if request.method == 'POST':
        print('POST data:', request.POST)

        # Create a copy of POST data to modify
        post_data = request.POST.copy()

        # Get employee_id from POST data
        employee_id = post_data.get('employee_id')
        ministry_number = post_data.get('ministry_number')

        # If we have ministry_number but no employee_id, try to get employee_id
        if ministry_number and not employee_id:
            try:
                employee = Employee.objects.get(ministry_number=ministry_number)
                post_data['employee_id'] = str(employee.id)
                post_data['employee'] = str(employee.id)
                print(f'Found employee by ministry number: {employee.id} - {employee.full_name}')
            except Employee.DoesNotExist:
                messages.error(request, 'لم يتم العثور على موظف بهذا الرقم الوزاري')
                return render(request, 'leaves/leave_balance_form.html', {'form': LeaveBalanceForm(post_data)})

        # If we have employee_id but no employee, set employee
        if employee_id and not post_data.get('employee'):
            try:
                employee = Employee.objects.get(id=employee_id)
                post_data['employee'] = str(employee.id)
                print(f'Found employee by ID: {employee.id} - {employee.full_name}')
            except Employee.DoesNotExist:
                messages.error(request, 'لم يتم العثور على موظف بهذا الرقم')
                return render(request, 'leaves/leave_balance_form.html', {'form': LeaveBalanceForm(post_data)})

        # Create form with modified POST data
        form = LeaveBalanceForm(post_data)
        print('Modified POST data:', post_data)

        if form.is_valid():
            print('Form is valid. Cleaned data:', form.cleaned_data)

            try:
                # Get the employee
                employee_id = post_data.get('employee')
                if not employee_id:
                    employee_id = post_data.get('employee_id')

                if not employee_id:
                    messages.error(request, 'الرجاء اختيار موظف')
                    return render(request, 'leaves/leave_balance_form.html', {'form': form})

                try:
                    employee = Employee.objects.get(id=employee_id)
                except Employee.DoesNotExist:
                    messages.error(request, 'لم يتم العثور على موظف بهذا الرقم')
                    return render(request, 'leaves/leave_balance_form.html', {'form': form})

                # Get other fields
                leave_type = form.cleaned_data.get('leave_type')
                year = form.cleaned_data.get('year')
                initial_balance = form.cleaned_data.get('initial_balance')
                used_balance = form.cleaned_data.get('used_balance', 0)

                if not leave_type:
                    messages.error(request, 'الرجاء اختيار نوع الإجازة')
                    return render(request, 'leaves/leave_balance_form.html', {'form': form})

                if not year:
                    messages.error(request, 'الرجاء إدخال السنة')
                    return render(request, 'leaves/leave_balance_form.html', {'form': form})

                if initial_balance is None:
                    messages.error(request, 'الرجاء إدخال الرصيد الأولي')
                    return render(request, 'leaves/leave_balance_form.html', {'form': form})

                # Check for duplicate
                existing_balance = LeaveBalance.objects.filter(
                    employee=employee,
                    leave_type=leave_type,
                    year=year
                ).first()

                if existing_balance:
                    messages.error(request, 'يوجد بالفعل رصيد إجازة لهذا الموظف ونوع الإجازة والسنة.')
                    return render(request, 'leaves/leave_balance_form.html', {'form': form})

                # Create a new balance
                balance = LeaveBalance(
                    employee=employee,
                    leave_type=leave_type,
                    year=year,
                    initial_balance=initial_balance,
                    used_balance=used_balance
                )

                # Save the balance
                balance.save()
                print('Balance saved successfully with ID:', balance.id)

                messages.success(request, 'تم إضافة رصيد الإجازة بنجاح.')
                return redirect('leaves:leave_balance_list')

            except Exception as e:
                print('Error saving balance:', str(e))
                messages.error(request, f'حدث خطأ أثناء حفظ رصيد الإجازة: {str(e)}')
                return render(request, 'leaves/leave_balance_form.html', {'form': form})
        else:
            print('Form is invalid. Errors:', form.errors)
            for field, errors in form.errors.items():
                print(f'Field {field} errors: {errors}')
    else:
        form = LeaveBalanceForm()

    return render(request, 'leaves/leave_balance_form.html', {'form': form})

@login_required
def leave_balance_update(request, pk):
    balance = get_object_or_404(LeaveBalance, pk=pk)

    if request.method == 'POST':
        print('POST data received:', request.POST)

        # Create a mutable copy of POST data
        post_data = request.POST.copy()

        # Get ministry number and employee_id from POST data
        ministry_number = post_data.get('ministry_number', '').strip()
        employee_id = post_data.get('employee_id')

        print(f'Ministry number: {ministry_number}, Employee ID: {employee_id}')

        # If we have ministry_number but no employee_id, try to get employee_id
        if ministry_number and not employee_id:
            try:
                employee = Employee.objects.get(ministry_number=ministry_number)
                post_data['employee_id'] = str(employee.id)
                post_data['employee'] = str(employee.id)
                print(f'Found employee by ministry number: {employee.id} - {employee.full_name}')
            except Employee.DoesNotExist:
                messages.error(request, 'لم يتم العثور على موظف بهذا الرقم الوزاري')
                form = LeaveBalanceForm(post_data, instance=balance)
                # Populate form with existing data
                form.fields['ministry_number'].initial = balance.employee.ministry_number
                form.fields['employee_name'].initial = balance.employee.full_name
                return render(request, 'leaves/leave_balance_form.html', {'form': form, 'balance': balance})

        # If we have employee_id but no employee, set employee
        if employee_id and not post_data.get('employee'):
            try:
                employee = Employee.objects.get(id=employee_id)
                post_data['employee'] = str(employee.id)
                print(f'Found employee by ID: {employee.id} - {employee.full_name}')
            except Employee.DoesNotExist:
                messages.error(request, 'لم يتم العثور على موظف بهذا الرقم')
                form = LeaveBalanceForm(post_data, instance=balance)
                # Populate form with existing data
                form.fields['ministry_number'].initial = balance.employee.ministry_number
                form.fields['employee_name'].initial = balance.employee.full_name
                return render(request, 'leaves/leave_balance_form.html', {'form': form, 'balance': balance})

        # Create form with modified POST data
        form = LeaveBalanceForm(post_data, instance=balance)
        print('Modified POST data:', post_data)

        if form.is_valid():
            print('Form is valid. Cleaned data:', form.cleaned_data)
            try:
                # Save the balance
                updated_balance = form.save()
                print(f'Balance updated successfully: {updated_balance.id}')
                messages.success(request, 'تم تحديث رصيد الإجازة بنجاح.')
                return redirect('leaves:leave_balance_list')
            except Exception as e:
                print('Error updating balance:', str(e))
                messages.error(request, f'حدث خطأ أثناء تحديث رصيد الإجازة: {str(e)}')
                return render(request, 'leaves/leave_balance_form.html', {'form': form, 'balance': balance})
        else:
            print('Form is invalid. Errors:', form.errors)
            for field, errors in form.errors.items():
                print(f'Field {field} errors: {errors}')
    else:
        # GET request - populate form with existing data
        form = LeaveBalanceForm(instance=balance)
        # Set the display fields
        form.fields['ministry_number'].initial = balance.employee.ministry_number
        form.fields['employee_name'].initial = balance.employee.full_name
        form.fields['employee_id'].initial = balance.employee.id

    return render(request, 'leaves/leave_balance_form.html', {'form': form, 'balance': balance})

@login_required
def leave_balance_delete(request, pk):
    balance = get_object_or_404(LeaveBalance, pk=pk)
    if request.method == 'POST':
        balance.delete()
        messages.success(request, 'تم حذف رصيد الإجازة بنجاح.')
        return redirect('leaves:leave_balance_list')
    return render(request, 'leaves/leave_balance_confirm_delete.html', {'balance': balance})

@login_required
def departure_list(request):
    departures = Departure.objects.all()
    return render(request, 'leaves/departure_list.html', {'departures': departures})

@login_required
def departure_create(request):
    if request.method == 'POST':
        form = DepartureForm(request.POST)
        if form.is_valid():
            departure = form.save(commit=False)

            # Get employee from employee_id
            employee_id = request.POST.get('employee_id')
            if employee_id:
                try:
                    employee = Employee.objects.get(id=employee_id)
                    departure.employee = employee
                except Employee.DoesNotExist:
                    messages.error(request, 'لم يتم العثور على الموظف.')
                    return render(request, 'leaves/departure_form.html', {'form': form})

            departure.save()
            messages.success(request, 'تم إضافة المغادرة بنجاح.')
            return redirect('leaves:departure_list')
    else:
        form = DepartureForm()
    return render(request, 'leaves/departure_form.html', {'form': form})

@login_required
def leave_reports(request):
    # Get all employees with their leave balances
    employees = Employee.objects.all()
    leave_types = LeaveType.objects.all()
    current_year = timezone.now().year

    # Prepare data in a format that doesn't require custom template filters
    employee_balances = []
    for employee in employees:
        employee_data = {'employee': employee, 'type_balances': []}

        for leave_type in leave_types:
            try:
                balance = LeaveBalance.objects.get(
                    employee=employee,
                    leave_type=leave_type,
                    year=current_year
                )
                type_balance = {
                    'leave_type': leave_type,
                    'initial': balance.initial_balance,
                    'used': balance.used_balance,
                    'remaining': balance.remaining_balance
                }
            except LeaveBalance.DoesNotExist:
                type_balance = {
                    'leave_type': leave_type,
                    'initial': 0,
                    'used': 0,
                    'remaining': 0
                }

            employee_data['type_balances'].append(type_balance)

        employee_balances.append(employee_data)

    return render(request, 'leaves/leave_reports.html', {
        'employee_balances': employee_balances,
        'leave_types': leave_types
    })