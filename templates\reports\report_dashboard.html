{% extends 'base.html' %}
{% load static %}

{% block title %}التقارير والإحصائيات - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/report-cards.css' %}">
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>التقارير والإحصائيات</h2>
</div>

<div class="row">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            الموظفين</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ employee_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            الأقسام</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ department_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-building fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            الإجازات</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ leave_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            تقييمات الأداء</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ evaluation_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">أنواع التقارير</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">تقرير الحضور</h5>
                                <p class="card-text">تقرير عن حضور الموظفين والإجازات خلال فترة محددة.</p>
                                <a href="{% url 'reports:attendance_report' %}" class="btn btn-dark">إنشاء التقرير</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">تقرير الكادر</h5>
                                <p class="card-text">تقرير عن حالة الكادر والتعيينات في المؤسسة.</p>
                                <a href="{% url 'reports:employment_report' %}" class="btn btn-dark">إنشاء التقرير</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">تقرير الموقف الفني</h5>
                                <p class="card-text">تقرير عن المواقف الفنية والشواغر المتاحة.</p>
                                <a href="{% url 'reports:technical_position_report' %}" class="btn btn-dark">إنشاء التقرير</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">تقرير الكادر حسب الجنس</h5>
                                <p class="card-text">تقرير عن بيانات الكادر كاملة أو حسب الجنس (ذكر أو أنثى).</p>
                                <a href="{% url 'reports:staff_report' %}" class="btn btn-dark">إنشاء التقرير</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">تقرير موظف</h5>
                                <p class="card-text">تقرير شامل عن موظف محدد وبياناته الكاملة.</p>
                                <a href="{% url 'reports:employee_selection' %}" class="btn btn-dark">اختيار موظف</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">تقرير قسم</h5>
                                <p class="card-text">تقرير عن قسم محدد والموظفين العاملين فيه.</p>
                                <a href="{% url 'reports:department_selection' %}" class="btn btn-dark">اختيار قسم</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">تقرير الإجازات</h5>
                                <p class="card-text">تقرير عن الإجازات خلال فترة محددة.</p>
                                <a href="{% url 'reports:leave_report' %}" class="btn btn-dark">إنشاء التقرير</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">تقرير المسمى الوظيفي</h5>
                                <p class="card-text">تقرير عن الموظفين حسب المسميات الوظيفية.</p>
                                <a href="{% url 'reports:position_report' %}" class="btn btn-dark">إنشاء التقرير</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">تقرير التخصص</h5>
                                <p class="card-text">تقرير عن الموظفين حسب التخصص.</p>
                                <a href="{% url 'reports:specialization_report' %}" class="btn btn-dark">إنشاء التقرير</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">تقرير الإجازات بدون راتب</h5>
                                <p class="card-text">تقرير عن الموظفين الذين حصلوا على إجازات بدون راتب.</p>
                                <a href="{% url 'reports:unpaid_leave_report' %}" class="btn btn-dark">إنشاء التقرير</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">تقرير موظفي المديرية</h5>
                                <p class="card-text">تقرير عن الموظفين الذين مكان العمل لهم المديرية مع إمكانية تصدير التقرير لكل قسم أو المديرية كاملة.</p>
                                <a href="{% url 'reports:directorate_employees_report' %}" class="btn btn-dark">إنشاء التقرير</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">التقارير الأخيرة</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>العنوان</th>
                                <th>النوع</th>
                                <th>تاريخ الإنشاء</th>
                                <th>بواسطة</th>
                                <th>الإجراءات</th>
                                <th>الحذف</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for report in recent_reports %}
                            <tr>
                                <td>{{ report.title }}</td>
                                <td>{{ report.get_report_type_display }}</td>
                                <td>{{ report.display_time }}</td>
                                <td>{{ report.created_by|default:"-" }}</td>
                                <td>
                                    {% if report.file %}
                                    <a href="{{ report.file.url }}" class="btn btn-success btn-sm" download>
                                        <i class="fas fa-download"></i> تنزيل
                                    </a>
                                    {% else %}
                                    <span class="badge bg-secondary">لا يوجد ملف</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="#" class="btn btn-danger btn-sm delete-report" data-id="{{ report.pk }}">
                                        <i class="fas fa-trash"></i> حذف
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center">لا يوجد تقارير</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add event listeners for delete buttons
    document.addEventListener('DOMContentLoaded', function() {
        const deleteButtons = document.querySelectorAll('.delete-report');

        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const id = this.getAttribute('data-id');
                if (confirm('هل أنت متأكد من حذف هذا التقرير؟')) {
                    // Implement delete functionality
                    window.location.href = `/reports/${id}/delete/`;
                }
            });
        });
    });
</script>
{% endblock %}