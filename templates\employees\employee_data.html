{% extends 'base.html' %}
{% load static %}

{% block title %}بيانات الموظفين - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .form-group {
        margin-bottom: 1rem;
    }
    .form-group label {
        font-weight: bold;
    }
    .delete-confirmation {
        background-color: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 5px;
    }
    .filters-section {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border: 1px solid #dee2e6;
        margin-bottom: 0;
    }
    .filter-control {
        font-size: 0.95rem;
        padding: 8px 12px;
        height: auto;
        border: 1px solid #ced4da;
    }

    .filter-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* تحسين مظهر الأزرار */
    .btn-filter {
        padding: 8px 12px;
        height: 100%;
    }

    /* Filter section styling */
    .filter-select {
        font-size: 13px;
    }

    .select2-container--default .select2-selection--single {
        height: 31px;
        border: 1px solid #d1d3e2;
        border-radius: 0.35rem;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 29px;
        font-size: 13px;
        color: #5a5c69;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 29px;
    }

    .select2-dropdown {
        border: 1px solid #d1d3e2;
        border-radius: 0.35rem;
    }

    .form-label.small {
        font-weight: 600;
        margin-bottom: 5px;
    }

    #filterInfo {
        background-color: #f8f9fc;
        padding: 8px 12px;
        border-radius: 0.35rem;
        border-left: 4px solid #4e73df;
    }

    /* Active filter styling */
    .select2-container.filter-active .select2-selection--single {
        background-color: #e3f2fd !important;
        border-color: #2196f3 !important;
        box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25) !important;
    }

    .select2-container.filter-active .select2-selection__rendered {
        color: #1976d2 !important;
        font-weight: 600 !important;
    }

    /* Filter section animations */
    .filter-select {
        transition: all 0.3s ease;
    }

    .select2-container {
        transition: all 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>بيانات الموظفين</h2>
    <div>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addEmployeeModal">
            <i class="fas fa-user-plus"></i> إضافة موظف جديد
        </button>
        <a href="{% url 'employees:employee_import_export' %}" class="btn btn-success">
            <i class="fas fa-file-import"></i> استيراد / تصدير
        </a>
        <a href="{% url 'employees:employee_list' %}" class="btn btn-secondary">
            <i class="fas fa-sync"></i> تحديث
        </a>
    </div>
</div>

{% if is_delete %}
<div class="row mb-4">
    <div class="col-md-6 mx-auto">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="m-0"><i class="fas fa-exclamation-triangle me-2"></i> تأكيد الحذف</h5>
            </div>
            <div class="card-body">
                <h5 class="card-title">هل أنت متأكد من حذف هذا الموظف؟</h5>
                <div class="alert alert-light">
                    <p>
                        <strong>الرقم الوزاري:</strong> {{ employee_to_delete.ministry_number }}<br>
                        <strong>الاسم الرباعي:</strong> {{ employee_to_delete.full_name }}<br>
                        <strong>القسم:</strong> {{ employee_to_delete.school }}
                    </p>
                    <p class="mb-0 text-danger"><i class="fas fa-exclamation-circle"></i> هذا الإجراء لا يمكن التراجع عنه.</p>
                </div>

                <form method="post" action="{% url 'employees:employee_delete' employee_to_delete.pk %}" class="mt-3">
                    {% csrf_token %}
                    <div class="d-grid gap-2 d-md-flex justify-content-center">
                        <a href="{% url 'employees:employee_list' %}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> تأكيد الحذف
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Filters Section -->
<div class="card shadow mb-3">
    <div class="card-header py-2">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-filter"></i> فلاتر البحث
        </h6>
    </div>
    <div class="card-body py-3">
        <div class="row g-3">
            <!-- Specialization Filter -->
            <div class="col-md-2">
                <label class="form-label small">
                    <i class="fas fa-book text-info"></i> التخصص
                </label>
                <select class="form-select form-select-sm filter-select select2-filter" id="specializationFilter" data-column="7">
                    <option value="">جميع التخصصات</option>
                    {% for spec in specializations %}
                    <option value="{{ spec }}" {% if specialization_filter == spec %}selected{% endif %}>{{ spec }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- Appointment Type Filter -->
            <div class="col-md-2">
                <label class="form-label small">
                    <i class="fas fa-file-signature text-warning"></i> صفة التعيين
                </label>
                <select class="form-select form-select-sm filter-select select2-filter" id="appointmentTypeFilter" data-column="9">
                    <option value="">جميع الصفات</option>
                    {% for apt in appointment_types %}
                    <option value="{{ apt.name }}" {% if appointment_type_filter == apt.id|stringformat:"s" %}selected{% endif %}>{{ apt.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- Department Filter -->
            <div class="col-md-2">
                <label class="form-label small">
                    <i class="fas fa-building text-success"></i> القسم
                </label>
                <select class="form-select form-select-sm filter-select select2-filter" id="departmentFilter" data-column="10">
                    <option value="">جميع الأقسام</option>
                    {% for dept in unique_departments %}
                    <option value="{{ dept }}" {% if department_filter == dept %}selected{% endif %}>{{ dept }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- Position Filter -->
            <div class="col-md-2">
                <label class="form-label small">
                    <i class="fas fa-briefcase text-secondary"></i> المسمى الوظيفي
                </label>
                <select class="form-select form-select-sm filter-select select2-filter" id="positionFilter" data-column="11">
                    <option value="">جميع المسميات</option>
                    {% for pos in positions %}
                    <option value="{{ pos.name }}" {% if position_filter == pos.id|stringformat:"s" %}selected{% endif %}>{{ pos.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- Gender Filter -->
            <div class="col-md-2">
                <label class="form-label small">
                    <i class="fas fa-venus-mars text-danger"></i> الجنس
                </label>
                <select class="form-select form-select-sm filter-select select2-filter" id="genderFilter" data-column="4">
                    <option value="">جميع الأنواع</option>
                    <option value="ذكر" {% if gender_filter == "male" %}selected{% endif %}>ذكر</option>
                    <option value="أنثى" {% if gender_filter == "female" %}selected{% endif %}>أنثى</option>
                </select>
            </div>

            <!-- Reset Button -->
            <div class="col-md-2 d-flex align-items-end">
                <button type="button" class="btn btn-outline-secondary btn-sm w-100" id="resetFilters">
                    <i class="fas fa-undo"></i> إعادة ضبط
                </button>
            </div>
        </div>

        <!-- Filter Results Info -->
        <div class="row mt-2">
            <div class="col-12">
                <small class="text-muted" id="filterInfo">
                    <i class="fas fa-info-circle"></i>
                    عرض جميع الموظفين
                </small>
            </div>
        </div>
    </div>
</div>

{% if is_update or is_import_export %}
<div class="row">
    <!-- نموذج إضافة/تعديل الموظف -->
    <div class="col-md-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    {% if is_update %}تعديل بيانات {{ employee.full_name }}
                    {% elif is_import_export %}استيراد / تصدير بيانات الموظفين
                    {% endif %}
                </h6>
            </div>
            <div class="card-body">
{% endif %}

                {% if is_import_export %}
                <div class="import-export-section">
                    <h5>استيراد بيانات الموظفين</h5>
                    <div class="alert alert-info mb-3">
                        <p>يمكنك استيراد بيانات الموظفين من ملف Excel. يجب أن يحتوي الملف على الأعمدة المطلوبة.</p>
                    </div>

                    <form method="post" enctype="multipart/form-data" action="{% url 'employees:employee_import_export' %}">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="{{ import_form.excel_file.id_for_label }}" class="form-label">ملف Excel</label>
                            {{ import_form.excel_file }}
                            {% if import_form.excel_file.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in import_form.excel_file.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="d-grid gap-2 mb-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-file-import"></i> استيراد
                            </button>
                        </div>
                    </form>

                    <h5>تصدير بيانات الموظفين</h5>
                    <div class="alert alert-info mb-3">
                        <p>يمكنك تصدير بيانات الموظفين إلى ملف Excel أو PDF.</p>
                    </div>

                    <div class="d-grid gap-2 d-md-flex">
                        <a href="{% url 'employees:employee_import_export' %}?export=excel" class="btn btn-success me-md-2">
                            <i class="fas fa-file-excel"></i> تصدير إلى Excel
                        </a>
                        <a href="{% url 'employees:employee_import_export' %}?export=pdf" class="btn btn-danger">
                            <i class="fas fa-file-pdf"></i> تصدير إلى PDF
                        </a>
                    </div>
                </div>
                {% else %}





                </form>
                {% endif %}


    <!-- قائمة الموظفين -->
    <div class="{% if is_update or is_import_export %}col-md-7{% elif is_delete %}col-md-12{% else %}col-md-12{% endif %}">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">جميع الموظفين</h6>
                <div class="d-flex align-items-center">
                    <div class="input-group" style="width: 250px;">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" id="searchInput" class="form-control form-control-sm"
                               placeholder="بحث في الموظفين..." value="{{ search_query|default:'' }}">
                    </div>
                    <span class="badge bg-info ms-2" id="employeeCounter">{{ employees.count }} موظف</span>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="employeesTable">
                        <thead class="table-light">
                            <tr>
                                <th width="3%"><i class="fas fa-hashtag text-primary me-1"></i> الرقم</th>
                                <th><i class="fas fa-id-card me-1"></i> الرقم الوزاري</th>
                                <th><i class="fas fa-id-badge me-1"></i> الرقم الوطني</th>
                                <th><i class="fas fa-user me-1"></i> الاسم الرباعي</th>
                                <th><i class="fas fa-venus-mars me-1"></i> الجنس</th>
                                <th><i class="fas fa-birthday-cake me-1"></i> تاريخ الميلاد</th>
                                <th><i class="fas fa-graduation-cap me-1"></i> المؤهل العلمي</th>
                                <th><i class="fas fa-book me-1"></i> التخصص</th>
                                <th><i class="fas fa-calendar-check me-1"></i> تاريخ التعيين</th>
                                <th><i class="fas fa-file-signature me-1"></i> صفة التعيين</th>
                                <th><i class="fas fa-building me-1"></i> القسم</th>
                                <th><i class="fas fa-briefcase me-1"></i> المسمى الوظيفي</th>
                                <th><i class="fas fa-phone me-1"></i> رقم الهاتف</th>
                                <th><i class="fas fa-map-marker-alt me-1"></i> العنوان</th>
                                <th><i class="fas fa-cogs me-1"></i> الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in employees %}
                            <tr>
                                <td class="text-center fw-bold text-primary">{{ forloop.counter }}</td>
                                <td>{{ employee.ministry_number }}</td>
                                <td>{{ employee.national_id|default:"-" }}</td>
                                <td>
                                    <a href="{% url 'employees:employee_detail' employee.pk %}" class="text-decoration-none fw-bold text-primary">
                                        {{ employee.full_name }}
                                    </a>
                                </td>
                                <td>
                                    {% if employee.gender == 'male' %}
                                        <span class="badge bg-primary">{{ employee.get_gender_display }}</span>
                                    {% else %}
                                        <span class="badge bg-danger">{{ employee.get_gender_display }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <i class="far fa-calendar-alt text-primary me-1"></i>
                                    {{ employee.birth_date|date:"Y-m-d"|default:"-" }}
                                </td>
                                <td>{{ employee.qualification|default:"-" }}</td>
                                <td>{{ employee.specialization|default:"-" }}</td>
                                <td>
                                    <i class="far fa-calendar-check text-primary me-1"></i>
                                    {{ employee.hire_date|date:"Y-m-d" }}
                                </td>
                                <td>
                                    {% with current_employment=employee.employments.all.first %}
                                        {% if current_employment and current_employment.appointment_type %}
                                            {{ current_employment.appointment_type.name }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    {% endwith %}
                                </td>
                                <td>{{ employee.school }}</td>
                                <td>{{ employee.get_latest_position_with_date }}</td>
                                <td>
                                    {% if employee.phone_number %}
                                        <a href="tel:{{ employee.phone_number }}" class="text-decoration-none">
                                            <i class="fas fa-phone-alt text-primary me-1"></i>
                                            {{ employee.phone_number }}
                                        </a>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if employee.address %}
                                        <i class="fas fa-map-marker-alt text-primary me-1"></i>
                                        {{ employee.address }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{% url 'employees:employee_detail' employee.pk %}" class="btn btn-info btn-sm" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'employees:employee_update' employee.pk %}" class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'employees:employee_delete' employee.pk %}" class="btn btn-danger btn-sm" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="15" class="text-center p-5">
                                    <div class="py-5">
                                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                        <h5>لا يوجد موظفين</h5>
                                        <p class="text-muted">لم يتم العثور على أي موظفين يطابقون معايير البحث</p>
                                        <button type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#addEmployeeModal">
                                            <i class="fas fa-user-plus"></i> إضافة موظف جديد
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize Select2 for filters
    $('.select2-filter').select2({
        placeholder: function() {
            return $(this).find('option:first').text();
        },
        allowClear: true,
        width: '100%',
        language: {
            noResults: function() {
                return "لا توجد نتائج مطابقة";
            },
            searching: function() {
                return "جاري البحث...";
            }
        },
        minimumInputLength: 0
    });

    // Search functionality
    $('#searchInput').on('keyup', function() {
        applyAllFilters();
    });

    // Filter functionality
    $('.filter-select').on('change', function() {
        // Add visual feedback for active filters
        var $container = $(this).next('.select2-container');
        if ($(this).val()) {
            $container.addClass('filter-active');
        } else {
            $container.removeClass('filter-active');
        }
        applyAllFilters();
    });

    // Reset filters
    $('#resetFilters').on('click', function() {
        $('.filter-select').val('').trigger('change');
        $('#searchInput').val('');
        applyAllFilters();
    });

    // Apply all filters function
    function applyAllFilters() {
        var searchValue = $('#searchInput').val().toLowerCase();
        var specializationFilter = $('#specializationFilter').val();
        var appointmentTypeFilter = $('#appointmentTypeFilter').val();
        var departmentFilter = $('#departmentFilter').val();
        var positionFilter = $('#positionFilter').val();
        var genderFilter = $('#genderFilter').val();

        var visibleCount = 0;
        var totalCount = 0;

        $('#employeesTable tbody tr').each(function() {
            var $row = $(this);
            var rowText = $row.text().toLowerCase();
            var show = true;
            totalCount++;

            // Skip empty row
            if ($row.find('td').length === 1) {
                $row.hide();
                totalCount--;
                return;
            }

            // Search filter
            if (searchValue && rowText.indexOf(searchValue) === -1) {
                show = false;
            }

            // Specialization filter
            if (specializationFilter && $row.find('td:eq(7)').text().trim() !== specializationFilter) {
                show = false;
            }

            // Appointment type filter
            if (appointmentTypeFilter && $row.find('td:eq(9)').text().trim() !== appointmentTypeFilter) {
                show = false;
            }

            // Department filter
            if (departmentFilter && $row.find('td:eq(10)').text().trim() !== departmentFilter) {
                show = false;
            }

            // Position filter - check if position text contains the filter value
            if (positionFilter) {
                var positionText = $row.find('td:eq(11)').text().trim();
                if (positionText === '-' || positionText.indexOf(positionFilter) === -1) {
                    show = false;
                }
            }

            // Gender filter
            if (genderFilter) {
                var genderBadge = $row.find('td:eq(4) .badge').text().trim();
                if (genderBadge !== genderFilter) {
                    show = false;
                }
            }

            if (show) {
                $row.show();
                visibleCount++;
            } else {
                $row.hide();
            }
        });

        // Update counter
        $('#employeeCounter').text(visibleCount + ' من ' + totalCount + ' موظف');

        // Update filter info
        updateFilterInfo(searchValue, specializationFilter, appointmentTypeFilter, departmentFilter, positionFilter, genderFilter, visibleCount);
    }

    // Update filter info function
    function updateFilterInfo(search, specialization, appointmentType, department, position, gender, count) {
        var filters = [];

        if (search) filters.push('البحث: "' + search + '"');
        if (specialization) filters.push('التخصص: ' + specialization);
        if (appointmentType) filters.push('صفة التعيين: ' + appointmentType);
        if (department) filters.push('القسم: ' + department);
        if (position) filters.push('المسمى الوظيفي: ' + position);
        if (gender) filters.push('الجنس: ' + gender);

        var infoText = '';
        if (filters.length > 0) {
            infoText = '<i class="fas fa-filter text-primary"></i> مُطبق: ' + filters.join(' | ') + ' - النتائج: ' + count;
        } else {
            infoText = '<i class="fas fa-info-circle"></i> عرض جميع الموظفين - المجموع: ' + count;
        }

        $('#filterInfo').html(infoText);
    }

    // Initialize filter states on page load
    $('.filter-select').each(function() {
        if ($(this).val()) {
            $(this).next('.select2-container').addClass('filter-active');
        }
    });

    // Initial filter application
    applyAllFilters();

    // Add Bootstrap classes to form fields
    const formControls = document.querySelectorAll('input, select, textarea');
    formControls.forEach(function(element) {
        element.classList.add('form-control');
    });

    // Enable edit modal fields
    const modals = document.querySelectorAll('.modal');
    modals.forEach(function(modal) {
        modal.addEventListener('shown.bs.modal', function() {
            const inputs = this.querySelectorAll('input, select, textarea');
            inputs.forEach(function(input) {
                input.removeAttribute('readonly');
                input.removeAttribute('disabled');
            });

            // Focus on the first input
            const firstInput = this.querySelector('input');
            if (firstInput) {
                firstInput.focus();
            }
        });
    });
});
</script>

<!-- Modal لإضافة موظف جديد -->
<div class="modal fade" id="addEmployeeModal" tabindex="-1" aria-labelledby="addEmployeeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addEmployeeModalLabel">إضافة موظف جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="{% url 'employees:employee_list' %}" id="addEmployeeForm" novalidate>
                    {% csrf_token %}

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ form.ministry_number.id_for_label }}">الرقم الوزاري</label>
                                {{ form.ministry_number }}
                                {% if form.ministry_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.ministry_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ form.national_id.id_for_label }}">الرقم الوطني</label>
                                {{ form.national_id }}
                                {% if form.national_id.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.national_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="{{ form.full_name.id_for_label }}">الاسم الرباعي</label>
                        {{ form.full_name }}
                        {% if form.full_name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.full_name.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ form.gender.id_for_label }}">الجنس</label>
                                {{ form.gender }}
                                {% if form.gender.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.gender.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ form.birth_date.id_for_label }}">تاريخ الميلاد</label>
                                {{ form.birth_date }}
                                {% if form.birth_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.birth_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ form.qualification.id_for_label }}">المؤهل العلمي</label>
                                {{ form.qualification }}
                                {% if form.qualification.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.qualification.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ form.specialization.id_for_label }}">التخصص</label>
                                {{ form.specialization }}
                                {% if form.specialization.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.specialization.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ form.hire_date.id_for_label }}">تاريخ التعيين</label>
                                {{ form.hire_date }}
                                {% if form.hire_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.hire_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="id_appointment_type">صفة التعيين</label>
                                <select name="appointment_type" id="id_appointment_type" class="form-control select2">
                                    <option value="">---------</option>
                                    {% for apt in appointment_types %}
                                        <option value="{{ apt.id }}">{{ apt.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ form.school.id_for_label }}">القسم</label>
                                {{ form.school }}
                                {% if form.school.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.school.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="id_position">المسمى الوظيفي</label>
                                <select name="position" id="id_position" class="form-control select2">
                                    <option value="">---------</option>
                                    {% for pos in positions %}
                                        <option value="{{ pos.id }}">{{ pos.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ form.phone_number.id_for_label }}">رقم الهاتف</label>
                                {{ form.phone_number }}
                                {% if form.phone_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.phone_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="{{ form.address.id_for_label }}">العنوان</label>
                                {{ form.address }}
                                {% if form.address.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.address.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>


                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="document.getElementById('addEmployeeForm').submit();">حفظ</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}