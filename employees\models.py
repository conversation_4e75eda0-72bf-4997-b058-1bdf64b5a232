from django.db import models
from django.utils.translation import gettext_lazy as _

class Employee(models.Model):
    """Model for storing employee personal information"""
    GENDER_CHOICES = [
        ('male', _('ذكر')),
        ('female', _('أنثى')),
    ]

    ministry_number = models.Char<PERSON>ield(_('Ministry Number'), max_length=20, unique=True)
    national_id = models.Char<PERSON><PERSON>(_('National ID'), max_length=20, unique=True)
    full_name = models.Char<PERSON><PERSON>(_('Full Name'), max_length=255)
    gender = models.Char<PERSON><PERSON>(_('Gender'), max_length=10, choices=GENDER_CHOICES, default='male')
    qualification = models.Char<PERSON><PERSON>(_('Qualification'), max_length=255)
    specialization = models.Char<PERSON>ield(_('Specialization'), max_length=255)
    hire_date = models.DateField(_('Hire Date'))
    school = models.Char<PERSON><PERSON>(_('Department'), max_length=255)
    birth_date = models.DateField(_('Birth Date'))
    address = models.TextField(_('Address'))
    phone_number = models.Char<PERSON><PERSON>(_('Phone Number'), max_length=20)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Employee')
        verbose_name_plural = _('Employees')
        ordering = ['full_name']

    def __str__(self):
        return f"{self.full_name} ({self.ministry_number})"

    def get_latest_position(self):
        """Get the latest position for this employee"""
        # First check if there's a position in the employee_position table
        latest_position = self.positions.order_by('-date_obtained').first()
        if latest_position:
            return latest_position.position.name

        # If no position in employee_position, check employment
        from employment.models import Employment
        current_employment = Employment.objects.filter(employee=self).first()
        if current_employment and current_employment.position:
            return current_employment.position.name

        return '-'

    def get_latest_position_with_date(self):
        """Get the latest position with date for this employee"""
        # First check if there's a position in the employee_position table
        latest_position = self.positions.order_by('-date_obtained').first()
        if latest_position:
            return f"{latest_position.position.name} ({latest_position.date_obtained.strftime('%Y-%m-%d')})"

        # If no position in employee_position, check employment
        from employment.models import Employment
        current_employment = Employment.objects.filter(employee=self).first()
        if current_employment and current_employment.position:
            return f"{current_employment.position.name} ({current_employment.start_date.strftime('%Y-%m-%d')})"

        return '-'

class AnnualReport(models.Model):
    """Model for storing employee annual reports"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='annual_reports')
    year = models.PositiveIntegerField(_('Year'))
    score = models.DecimalField(_('Score'), max_digits=5, decimal_places=2)
    notes = models.TextField(_('Notes'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Annual Report')
        verbose_name_plural = _('Annual Reports')
        ordering = ['-year']
        unique_together = ['employee', 'year']

    def __str__(self):
        return f"{self.employee.full_name} - {self.year}"

class Penalty(models.Model):
    """Model for storing employee penalties"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='penalties')
    date = models.DateField(_('Date'))
    description = models.TextField(_('Description'))
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Penalty')
        verbose_name_plural = _('Penalties')
        ordering = ['-date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.date}"
