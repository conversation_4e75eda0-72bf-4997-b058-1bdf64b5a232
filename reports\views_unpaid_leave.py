from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse
import pandas as pd
from io import BytesIO
from leaves.models import Leave, LeaveType
from employment.models import Department

@login_required
def unpaid_leave_report(request):
    # Get filter parameters
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    department_id = request.GET.get('department')

    # Get unpaid leave type
    try:
        unpaid_leave_type = LeaveType.objects.get(name=LeaveType.UNPAID)

        # Base query - filter by unpaid leave type
        leaves = Leave.objects.filter(leave_type=unpaid_leave_type)

        # Apply filters
        if start_date:
            leaves = leaves.filter(start_date__gte=start_date)
        if end_date:
            leaves = leaves.filter(end_date__lte=end_date)
        if department_id:
            # Get employees in the department
            department = Department.objects.get(id=department_id)
            employees_in_dept = department.employees.filter(is_current=True).values_list('employee_id', flat=True)
            leaves = leaves.filter(employee_id__in=employees_in_dept)

        # Order by start date
        leaves = leaves.order_by('-start_date')
    except LeaveType.DoesNotExist:
        leaves = Leave.objects.none()

    # Get all departments for the filter
    departments = Department.objects.all()

    # Handle export
    if 'export' in request.GET:
        export_format = request.GET.get('export', 'excel')

        if export_format == 'excel':
            # Create Excel file
            output = BytesIO()
            writer = pd.ExcelWriter(output, engine='xlsxwriter')

            # Convert queryset to DataFrame
            data = []
            for leave in leaves:
                data.append({
                    'الرقم الوزاري': leave.employee.ministry_number,
                    'اسم الموظف': leave.employee.full_name,
                    'تاريخ البداية': leave.start_date,
                    'تاريخ النهاية': leave.end_date,
                    'عدد الأيام': leave.days_count,
                    'السبب': leave.reason,
                    'الحالة': leave.get_status_display(),
                })

            df = pd.DataFrame(data)
            df.to_excel(writer, sheet_name='تقرير الإجازات بدون راتب', index=False)

            # Adjust column widths
            worksheet = writer.sheets['تقرير الإجازات بدون راتب']
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)

            writer.close()
            output.seek(0)

            # Create response
            response = HttpResponse(output.read(), content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            response['Content-Disposition'] = 'attachment; filename=unpaid_leaves_report.xlsx'
            return response

        elif export_format == 'pdf':
            # Always show preview page first
            return render(request, 'reports/unpaid_leave_pdf_preview.html', {
                'leaves': leaves,
                'departments': departments,
                'start_date': start_date,
                'end_date': end_date,
                'department_id': department_id,
            })

    return render(request, 'reports/unpaid_leave_report.html', {
        'leaves': leaves,
        'departments': departments,
        'start_date': start_date,
        'end_date': end_date,
        'department_id': department_id,
    })
