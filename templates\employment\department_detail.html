{% extends 'base.html' %}
{% load static %}

{% block title %}{{ department.name }} - الكادر - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>قسم: {{ department.name }}</h2>
    <div>
        <a href="{% url 'employment:department_update' department.pk %}" class="btn btn-warning">
            <i class="fas fa-edit"></i> تعديل
        </a>
        <a href="{% url 'employment:department_delete' department.pk %}" class="btn btn-danger">
            <i class="fas fa-trash"></i> حذف
        </a>
        <a href="{% url 'employment:department_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للأقسام
        </a>
    </div>
</div>

{% if employees|length > 0 %}
<div class="alert alert-warning mb-4">
    <div class="d-flex align-items-center mb-2">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>تنبيه: لا يمكن حذف القسم</strong>
    </div>
    <p>لا يمكن حذف القسم "{{ department.name }}" لأنه مرتبط بـ {{ employees|length }} موظف.</p>
    <p class="mb-0">يجب عليك أولاً نقل الموظفين إلى قسم آخر قبل محاولة الحذف.</p>
</div>
{% endif %}

<div class="row">
    <div class="col-md-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">بيانات القسم</h6>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <th class="bg-light" width="40%">اسم القسم</th>
                        <td>{{ department.name }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">الوصف</th>
                        <td>{{ department.description|default:"-" }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">مكان العمل</th>
                        <td>{{ department.get_workplace_display }}</td>
                    </tr>
                    {% if department.workplace == 'school' %}
                    <tr>
                        <th class="bg-light">تصنيف المدرسة</th>
                        <td>{{ department.get_school_type_display|default:"-" }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">جنس المدرسة</th>
                        <td>{{ department.get_school_gender_display|default:"-" }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">أعلى صف</th>
                        <td>
                            {% if department.highest_grade %}
                                <span class="badge bg-success">
                                    <i class="fas fa-arrow-up me-1"></i>
                                    {{ department.get_highest_grade_display }}
                                </span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th class="bg-light">أدنى صف</th>
                        <td>
                            {% if department.lowest_grade %}
                                <span class="badge bg-danger">
                                    <i class="fas fa-arrow-down me-1"></i>
                                    {{ department.get_lowest_grade_display }}
                                </span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endif %}
                    <tr>
                        <th class="bg-light">عدد الموظفين</th>
                        <td>{{ employees|length }}</td>
                    </tr>
                    <tr>
                        <th class="bg-light">تاريخ الإنشاء</th>
                        <td>{{ department.created_at|date:"Y-m-d" }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">الموظفين في القسم</h6>
                <a href="{% url 'reports:department_report' department.pk %}" class="btn btn-sm btn-success">
                    <i class="fas fa-file-excel"></i> تصدير إلى Excel
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>الرقم الوزاري</th>
                                <th>الاسم الكامل</th>
                                <th>المنصب</th>
                                <th>تاريخ التعيين</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employment in employees %}
                            <tr>
                                <td>{{ employment.employee.ministry_number }}</td>
                                <td>
                                    <a href="{% url 'employees:employee_detail' employment.employee.pk %}">
                                        {{ employment.employee.full_name }}
                                    </a>
                                </td>
                                <td>{{ employment.position.name }}</td>
                                <td>{{ employment.start_date }}</td>
                                <td>
                                    <a href="{% url 'employees:employee_detail' employment.employee.pk %}" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center">لا يوجد موظفين في هذا القسم</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
