{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام شؤون الموظفين{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" href="{% static 'img/moe_logo.svg' %}" type="image/svg+xml">
    <link rel="shortcut icon" href="{% static 'img/favicon.ico' %}" type="image/x-icon">

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Google Fonts - Tajawal -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/public.css' %}">

    <!-- Attractive Card Styles -->
    <style>
        /* Attractive Card Styles */
        .attractive-card {
            position: relative;
            border: none;
            transition: all 0.4s ease;
            overflow: hidden;
            z-index: 1;
            background-color: white;
        }

        .attractive-card::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #009688, #3f51b5, #ff5722, #009688);
            background-size: 400% 400%;
            z-index: -1;
            animation: borderGradient 6s ease infinite;
            border-radius: var(--border-radius-lg);
        }

        .attractive-card::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            right: 2px;
            bottom: 2px;
            background-color: white;
            border-radius: calc(var(--border-radius-lg) - 2px);
            z-index: -1;
        }

        .attractive-card:hover::before {
            animation: borderGradient 3s ease infinite;
        }

        @keyframes borderGradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .attractive-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }

        .attractive-card .card-body {
            z-index: 2;
            background-color: transparent;
        }

        /* Fix for card header in attractive cards */
        .attractive-card .card-header {
            position: relative;
            z-index: 2;
        }

        /* Ensure navbar links stay in one row */
        .navbar-nav {
            flex-direction: row !important;
            flex-wrap: nowrap !important;
        }

        .navbar-nav .nav-item {
            white-space: nowrap;
        }

        .navbar-nav .nav-link {
            padding: 0.5rem 0.75rem !important;
            font-size: 0.9rem;
        }

        /* Responsive adjustments for smaller screens */
        @media (max-width: 1200px) {
            .navbar-nav .nav-link {
                padding: 0.5rem 0.5rem !important;
                font-size: 0.85rem;
            }
        }

        @media (max-width: 992px) {
            .navbar-nav .nav-link {
                padding: 0.5rem 0.4rem !important;
                font-size: 0.8rem;
            }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="{% url 'home:home' %}">
                <i class="fas fa-building me-2"></i> نظام شؤون الموظفين
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'home' %}active{% endif %}" href="{% url 'home:home' %}">
                            <i class="fas fa-home me-1"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'internal_transfer' %}active{% endif %}" href="{% url 'home:internal_transfer' %}">
                            <i class="fas fa-exchange-alt me-1"></i> النقل الداخلي
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'important_links' %}active{% endif %}" href="{% url 'home:important_links' %}">
                            <i class="fas fa-link me-1"></i> روابط مهمة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'approved_forms_list' %}active{% endif %}" href="{% url 'home:approved_forms_list' %}">
                            <i class="fas fa-file-alt me-1"></i> النماذج المعتمدة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'leave_balance_inquiry' %}active{% endif %}" href="{% url 'home:leave_balance_inquiry' %}">
                            <i class="fas fa-calendar-check me-1"></i> رصيد الإجازات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'about' %}active{% endif %}" href="{% url 'home:about' %}">
                            <i class="fas fa-info-circle me-1"></i> حول
                        </a>
                    </li>
                </ul>
                <div class="d-flex">
                    {% if user.is_authenticated %}
                        <a href="{% url 'dashboard' %}" class="btn btn-outline-light me-2">
                            <i class="fas fa-tachometer-alt me-1"></i> لوحة التحكم
                        </a>
                        <a href="{% url 'accounts:logout' %}" class="btn btn-danger">
                            <i class="fas fa-sign-out-alt me-1"></i> تسجيل الخروج
                        </a>
                    {% else %}
                        <a href="{% url 'accounts:login' %}" class="btn btn-light">
                            <i class="fas fa-sign-in-alt me-1"></i> تسجيل الدخول
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        {% if messages %}
            <div class="container">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="footer text-center">
        <div class="container">
            <div class="row">
                <div class="col-md-6 text-md-end mb-3 mb-md-0">
                    <h5 class="mb-3">نظام شؤون الموظفين</h5>
                    <p class="mb-0">نظام متكامل لإدارة شؤون الموظفين والكادر البشري</p>
                </div>
                <div class="col-md-6 text-md-start">
                    <h5 class="mb-3">روابط سريعة</h5>
                    <ul class="list-inline">
                        <li class="list-inline-item"><a href="{% url 'home:home' %}" class="text-white">الرئيسية</a></li>
                        <li class="list-inline-item">|</li>
                        <li class="list-inline-item"><a href="{% url 'home:internal_transfer' %}" class="text-white">النقل الداخلي</a></li>
                        <li class="list-inline-item">|</li>
                        <li class="list-inline-item"><a href="{% url 'home:important_links' %}" class="text-white">روابط مهمة</a></li>
                        <li class="list-inline-item">|</li>
                        <li class="list-inline-item"><a href="{% url 'home:approved_forms_list' %}" class="text-white">النماذج المعتمدة</a></li>
                        <li class="list-inline-item">|</li>
                        <li class="list-inline-item"><a href="{% url 'home:leave_balance_inquiry' %}" class="text-white">رصيد الإجازات</a></li>
                        <li class="list-inline-item">|</li>
                        <li class="list-inline-item"><a href="{% url 'home:about' %}" class="text-white">حول</a></li>
                    </ul>
                </div>
            </div>
            <hr class="my-3" style="border-color: rgba(255,255,255,0.2);">
            <p class="mb-0">&copy;  جميع الحقوق محفوظة مديرية قصبة المفرق - المبرمج احمد العمري</p>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Custom JS with Enhanced Effects -->
    <script>
        $(document).ready(function() {
            // Add animation to cards
            $('.card').addClass('fade-in-up');

            // Add animation to hero section
            $('.hero-section').addClass('fade-in');

            // Add float animation to card icons
            $('.card .rounded-circle').addClass('float');

            // Add pulse animation to primary buttons in hero section
            $('.hero-section .btn-primary').addClass('pulse');

            // Add hover effect to navbar links
            $('.navbar-nav .nav-link').hover(
                function() {
                    $(this).find('i').addClass('fa-bounce');
                },
                function() {
                    $(this).find('i').removeClass('fa-bounce');
                }
            );

            // Add smooth scrolling
            $('a[href*="#"]:not([href="#"])').click(function() {
                if (location.pathname.replace(/^\//, '') == this.pathname.replace(/^\//, '') && location.hostname == this.hostname) {
                    var target = $(this.hash);
                    target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
                    if (target.length) {
                        $('html, body').animate({
                            scrollTop: target.offset().top - 70
                        }, 1000);
                        return false;
                    }
                }
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
