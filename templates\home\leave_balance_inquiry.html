{% extends 'home/base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    /* Add background colors for the table cells to match PDF preview */
    .bg-success.bg-opacity-25 {
        background-color: #d4edda !important;
    }

    .bg-danger.bg-opacity-25 {
        background-color: #f8d7da !important;
    }

    .bg-info.bg-opacity-25 {
        background-color: #d1ecf1 !important;
    }

    /* Add clear borders to table cells */
    .table-bordered {
        border: 2px solid #dee2e6 !important;
    }

    .table-bordered th,
    .table-bordered td {
        border: 1px solid #000 !important;
        border-width: 1px !important;
    }

    .table-bordered thead th {
        border-bottom: 2px solid #000 !important;
    }

    /* Make text in cells bold for better visibility */
    .table-bordered td {
        font-weight: bold;
    }

    .search-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .search-card .card-body {
        padding: 2rem;
    }

    .search-input {
        border-radius: 25px;
        border: none;
        padding: 12px 20px;
        font-size: 16px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .search-btn {
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: bold;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .result-card {
        display: none;
        animation: fadeInUp 0.5s ease-in-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .employee-info {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .print-btn {
        position: fixed;
        bottom: 30px;
        right: 30px;
        z-index: 1000;
        border-radius: 50%;
        width: 60px;
        height: 60px;
        display: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    @media print {
        .no-print {
            display: none !important;
        }

        .print-btn {
            display: none !important;
        }

        .card {
            border: none !important;
            box-shadow: none !important;
        }

        .employee-info {
            background: #f8f9fa !important;
            color: #000 !important;
            border: 1px solid #dee2e6 !important;
        }

        /* Hide header and footer when printing */
        .navbar,
        .footer,
        header,
        footer,
        nav {
            display: none !important;
        }

        /* Hide page title */
        .container h1,
        .container .h3 {
            display: none !important;
        }

        /* Hide base template elements */
        body::before,
        body::after {
            display: none !important;
        }

        /* Adjust margins for print */
        body {
            margin: 0 !important;
            padding: 0 !important;
        }

        .container {
            max-width: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        /* Hide specific base template elements */
        .hero-section,
        .navbar-brand,
        .navbar-nav,
        .navbar-toggler,
        .navbar-collapse {
            display: none !important;
        }

        /* Override any base template styles */
        * {
            background-image: none !important;
            background-color: white !important;
        }

        .employee-info * {
            background-color: #f8f9fa !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Header with back button -->
    <div class="d-flex justify-content-between align-items-center mb-4 no-print">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-calendar-check me-2"></i>
            {{ title }}
        </h1>
        <div>
            <a href="{% url 'home:home' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> العودة للرئيسية
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <!-- Search Card -->
            <div class="card shadow search-card mb-4">
                <div class="card-body text-center">
                    <h2 class="mb-4">
                        <i class="fas fa-calendar-check me-2"></i>
                        {{ title }}
                    </h2>
                    <p class="lead mb-4">الاستعلام عن رصيد الإجازات للموظفين في المديرية</p>

                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <div class="input-group">
                                <input type="text"
                                       id="searchInput"
                                       class="form-control search-input"
                                       placeholder="أدخل الرقم الوزاري أو الاسم..."
                                       autocomplete="off">
                                <button class="btn btn-light search-btn"
                                        type="button"
                                        id="searchBtn">
                                    <i class="fas fa-search me-2"></i>
                                    بحث
                                </button>
                            </div>
                            <small class="text-light mt-2 d-block">
                                يمكنك البحث باستخدام الرقم الوزاري أو الاسم الكامل أو جزء منه
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loading Indicator -->
            <div id="loadingIndicator" class="text-center" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري البحث...</span>
                </div>
                <p class="mt-2">جاري البحث...</p>
            </div>

            <!-- Error Alert -->
            <div id="errorAlert" class="alert alert-danger" style="display: none;">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <span id="errorMessage"></span>
            </div>

            <!-- Multiple Employees Alert -->
            <div id="multipleEmployeesAlert" class="alert alert-warning" style="display: none;">
                <h5><i class="fas fa-users me-2"></i>يوجد أكثر من موظف بهذا الاسم</h5>
                <p>يرجى استخدام الرقم الوزاري للبحث الدقيق، أو اختيار أحد الموظفين التاليين:</p>
                <div id="employeesList"></div>
            </div>

            <!-- Results Card -->
            <div id="resultsCard" class="card shadow result-card">
                <div class="card-header bg-primary text-white no-print">
                    <h4 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        نتائج البحث
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Employee Info -->
                    <div id="employeeInfo" class="employee-info">
                        <!-- Employee details will be populated here -->
                    </div>

                    <!-- Leave Balance Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="balanceTable" width="100%" cellspacing="0">
                            <thead class="thead-light">
                                <tr>
                                    <th rowspan="2" class="text-center align-middle">نوع الإجازة</th>
                                    <th colspan="3" class="text-center">رصيد الإجازات</th>
                                </tr>
                                <tr>
                                    <th class="bg-success text-white text-center">الرصيد الأولي</th>
                                    <th class="bg-danger text-white text-center">الإجازات المستخدمة</th>
                                    <th class="bg-info text-white text-center">الرصيد المتبقي</th>
                                </tr>
                            </thead>
                            <tbody id="balanceTableBody">
                                <!-- Balance data will be populated here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Information Section -->
                    <div class="mt-4 no-print">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>معلومات عن رصيد الإجازات</h6>
                            <ul class="mb-0">
                                <li><strong>الرصيد الأولي:</strong> رصيد الإجازة في بداية السنة</li>
                                <li><strong>الإجازات المستخدمة:</strong> عدد أيام الإجازة المستخدمة خلال السنة</li>
                                <li><strong>الرصيد المتبقي:</strong> الرصيد المتبقي من الإجازة</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Print Button -->
                    <div class="text-center mt-4 no-print">
                        <button class="btn btn-success btn-lg" onclick="printResults()">
                            <i class="fas fa-print me-2"></i>
                            طباعة
                        </button>
                        <button class="btn btn-secondary btn-lg ms-2" onclick="clearResults()">
                            <i class="fas fa-times me-2"></i>
                            مسح النتائج
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Floating Print Button -->
<button class="btn btn-success print-btn" onclick="printResults()" title="طباعة">
    <i class="fas fa-print"></i>
</button>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Handle search button click
    $('#searchBtn').click(function() {
        performSearch();
    });

    // Handle Enter key press in search input
    $('#searchInput').keypress(function(e) {
        if (e.which == 13) {
            performSearch();
        }
    });

    // Auto-focus on search input
    $('#searchInput').focus();
});

function performSearch() {
    const searchTerm = $('#searchInput').val().trim();

    if (!searchTerm) {
        showError('الرجاء إدخال الرقم الوزاري أو الاسم');
        return;
    }

    // Hide previous results and errors
    hideAllAlerts();
    $('#resultsCard').hide();
    $('.print-btn').hide();

    // Show loading indicator
    $('#loadingIndicator').show();

    // Make AJAX request
    $.ajax({
        url: '{% url "home:get_employee_leave_balance" %}',
        method: 'GET',
        data: {
            'search_term': searchTerm
        },
        success: function(response) {
            $('#loadingIndicator').hide();

            if (response.success) {
                displayResults(response);
            } else {
                if (response.multiple_employees) {
                    showMultipleEmployees(response.multiple_employees);
                } else {
                    showError(response.error);
                }
            }
        },
        error: function(xhr, status, error) {
            $('#loadingIndicator').hide();
            showError('حدث خطأ في الاتصال بالخادم');
        }
    });
}

function displayResults(data) {
    const employee = data.employee;
    const balances = data.balances;

    // Populate employee info
    const employeeInfoHtml = `
        <div class="row">
            <div class="col-md-6">
                <h4 style="color: white;"><i class="fas fa-user me-2"></i>${employee.full_name}</h4>
                <p class="mb-1" style="color: black;"><strong>الرقم الوزاري:</strong> ${employee.ministry_number}</p>
                <p class="mb-0" style="color: black;"><strong>القسم:</strong> ${employee.department}</p>
            </div>
            <div class="col-md-6 text-md-end">
                <h5 style="color: white;"><i class="fas fa-calendar me-2"></i>السنة: ${employee.year}</h5>
                <p class="mb-0" style="color: black;"><small>تاريخ الاستعلام: ${employee.inquiry_date}</small></p>
            </div>
        </div>
    `;
    $('#employeeInfo').html(employeeInfoHtml);

    // Populate balance table
    let tableBodyHtml = '';
    balances.forEach(function(balance) {
        tableBodyHtml += `
            <tr class="text-center">
                <td class="text-start"><strong>${balance.type_name}</strong></td>
                <td class="bg-success bg-opacity-25">${balance.initial_balance}</td>
                <td class="bg-danger bg-opacity-25">${balance.used_balance}</td>
                <td class="bg-info bg-opacity-25">${balance.remaining_balance}</td>
            </tr>
        `;
    });
    $('#balanceTableBody').html(tableBodyHtml);

    // Show results
    $('#resultsCard').show();
    $('.print-btn').show();

    // Scroll to results
    $('html, body').animate({
        scrollTop: $('#resultsCard').offset().top - 100
    }, 500);
}

function showError(message) {
    $('#errorMessage').text(message);
    $('#errorAlert').show();
}

function showMultipleEmployees(employees) {
    let employeesListHtml = '<div class="list-group">';
    employees.forEach(function(emp) {
        employeesListHtml += `
            <button type="button" class="list-group-item list-group-item-action"
                    onclick="selectEmployee('${emp.ministry_number}')">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${emp.full_name}</h6>
                    <small>الرقم الوزاري: ${emp.ministry_number}</small>
                </div>
                <p class="mb-1">${emp.school}</p>
            </button>
        `;
    });
    employeesListHtml += '</div>';

    $('#employeesList').html(employeesListHtml);
    $('#multipleEmployeesAlert').show();
}

function selectEmployee(ministryNumber) {
    $('#searchInput').val(ministryNumber);
    hideAllAlerts();
    performSearch();
}

function hideAllAlerts() {
    $('#errorAlert').hide();
    $('#multipleEmployeesAlert').hide();
}

function printResults() {
    window.print();
}

function clearResults() {
    $('#searchInput').val('');
    $('#resultsCard').hide();
    $('.print-btn').hide();
    hideAllAlerts();
    $('#searchInput').focus();
}
</script>
{% endblock %}
