{% extends 'base.html' %}
{% load static %}

{% block title %}العلاوات - الرتب والعلاوات - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        border-width: 2px !important;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
    }

    .stats-card .card-body {
        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    }

    .stats-card .h3 {
        font-size: 2.2rem;
        font-weight: 700;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    }

    .stats-card .card-title {
        font-size: 0.85rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .stats-card small {
        font-size: 0.75rem;
        opacity: 0.8;
    }

    .stats-card i {
        font-size: 1.1rem;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .stats-card .h3 {
            font-size: 1.8rem;
        }
        .stats-card .card-title {
            font-size: 0.75rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>العلاوات</h2>
    <div>
        <a href="{% url 'ranks:employee_allowance_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة علاوات موظف
        </a>
        <a href="?export=excel{% if search_term %}&search={{ search_term }}{% endif %}{% if education_allowance %}&education_allowance={{ education_allowance }}{% endif %}{% if adjustment_allowance %}&adjustment_allowance={{ adjustment_allowance }}{% endif %}{% if transportation_allowance %}&transportation_allowance={{ transportation_allowance }}{% endif %}{% if supervisory_allowance %}&supervisory_allowance={{ supervisory_allowance }}{% endif %}{% if technical_allowance %}&technical_allowance={{ technical_allowance }}{% endif %}" class="btn btn-success">
            <i class="fas fa-file-excel"></i> تصدير إلى Excel
        </a>
        <a href="{% url 'ranks:employee_rank_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للرتب
        </a>
    </div>
</div>

<!-- Alert for success messages -->
<div id="alert-container"></div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card stats-card border-start border-4 border-primary shadow rounded-3">
            <div class="card-body text-center py-3">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="fas fa-chart-bar text-primary me-2"></i>
                    <h6 class="card-title mb-0 text-primary">إجمالي السجلات</h6>
                </div>
                <div class="h3 mb-1 text-primary font-weight-bold">{{ total_allowances }}</div>
                <small class="text-muted">موظف</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card stats-card border-start border-4 border-success shadow rounded-3">
            <div class="card-body text-center py-3">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="fas fa-graduation-cap text-success me-2"></i>
                    <h6 class="card-title mb-0 text-success">علاوة التعليم</h6>
                </div>
                <div class="h3 mb-1 text-success font-weight-bold">{{ education_count }}</div>
                <small class="text-muted">من {{ total_allowances }}</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card stats-card border-start border-4 border-info shadow rounded-3">
            <div class="card-body text-center py-3">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="fas fa-cogs text-info me-2"></i>
                    <h6 class="card-title mb-0 text-info">التجيير</h6>
                </div>
                <div class="h3 mb-1 text-info font-weight-bold">{{ adjustment_count }}</div>
                <small class="text-muted">من {{ total_allowances }}</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card stats-card border-start border-4 border-warning shadow rounded-3">
            <div class="card-body text-center py-3">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="fas fa-car text-warning me-2"></i>
                    <h6 class="card-title mb-0 text-warning">التنقلات</h6>
                </div>
                <div class="h3 mb-1 text-warning font-weight-bold">{{ transportation_count }}</div>
                <small class="text-muted">من {{ total_allowances }}</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card stats-card border-start border-4 border-secondary shadow rounded-3">
            <div class="card-body text-center py-3">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="fas fa-user-tie text-secondary me-2"></i>
                    <h6 class="card-title mb-0 text-secondary">العلاوة الإشرافية</h6>
                </div>
                <div class="h3 mb-1 text-secondary font-weight-bold">{{ supervisory_count }}</div>
                <small class="text-muted">من {{ total_allowances }}</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card stats-card border-start border-4 border-dark shadow rounded-3">
            <div class="card-body text-center py-3">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="fas fa-tools text-dark me-2"></i>
                    <h6 class="card-title mb-0 text-dark">علاوة فنية</h6>
                </div>
                <div class="h3 mb-1 text-dark font-weight-bold">{{ technical_count }}</div>
                <small class="text-muted">من {{ total_allowances }}</small>
            </div>
        </div>
    </div>
</div>

<!-- Filters Section -->
<div class="card shadow mb-3">
    <div class="card-header py-2">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-filter"></i> فلاتر البحث
        </h6>
    </div>
    <div class="card-body py-3">
        <div class="row g-3">
            <!-- Education Allowance Filter -->
            <div class="col-md-2">
                <label class="form-label small">
                    <i class="fas fa-graduation-cap text-success"></i> علاوة التعليم
                </label>
                <select id="educationFilter" class="form-select form-select-sm filter-select" data-column="3">
                    <option value="">الكل</option>
                    <option value="نعم">نعم</option>
                    <option value="لا">لا</option>
                </select>
            </div>

            <!-- Adjustment Allowance Filter -->
            <div class="col-md-2">
                <label class="form-label small">
                    <i class="fas fa-cogs text-info"></i> التجيير
                </label>
                <select id="adjustmentFilter" class="form-select form-select-sm filter-select" data-column="4">
                    <option value="">الكل</option>
                    <option value="نعم">نعم</option>
                    <option value="لا">لا</option>
                </select>
            </div>

            <!-- Transportation Allowance Filter -->
            <div class="col-md-2">
                <label class="form-label small">
                    <i class="fas fa-car text-warning"></i> التنقلات
                </label>
                <select id="transportationFilter" class="form-select form-select-sm filter-select" data-column="5">
                    <option value="">الكل</option>
                    <option value="نعم">نعم</option>
                    <option value="لا">لا</option>
                </select>
            </div>

            <!-- Supervisory Allowance Filter -->
            <div class="col-md-2">
                <label class="form-label small">
                    <i class="fas fa-user-tie text-secondary"></i> العلاوة الإشرافية
                </label>
                <select id="supervisoryFilter" class="form-select form-select-sm filter-select" data-column="6">
                    <option value="">الكل</option>
                    <option value="نعم">نعم</option>
                    <option value="لا">لا</option>
                </select>
            </div>

            <!-- Technical Allowance Filter -->
            <div class="col-md-2">
                <label class="form-label small">
                    <i class="fas fa-tools text-dark"></i> علاوة فنية
                </label>
                <select id="technicalFilter" class="form-select form-select-sm filter-select" data-column="7">
                    <option value="">الكل</option>
                    <option value="نعم">نعم</option>
                    <option value="لا">لا</option>
                </select>
            </div>

            <!-- Reset Button -->
            <div class="col-md-2 d-flex align-items-end">
                <button type="button" id="resetFilters" class="btn btn-outline-secondary btn-sm w-100">
                    <i class="fas fa-undo"></i> إعادة ضبط
                </button>
            </div>
        </div>

        <!-- Filter Results Info -->
        <div class="row mt-2">
            <div class="col-12">
                <small class="text-muted" id="filterInfo">
                    <i class="fas fa-info-circle"></i>
                    عرض جميع العلاوات
                </small>
            </div>
        </div>
    </div>
</div>



<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">جميع العلاوات</h6>
        <div class="d-flex align-items-center">
            <div class="input-group" style="width: 250px;">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" id="searchInput" class="form-control form-control-sm"
                       placeholder="بحث في العلاوات...">
            </div>
            <span class="badge bg-info ms-2">{{ total_allowances }} سجل</span>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover" id="allowancesTable">
                <thead class="table-light">
                    <tr>
                        <th width="5%">
                            <i class="fas fa-hashtag text-primary"></i>
                            الرقم
                        </th>
                        <th width="12%">
                            <i class="fas fa-id-card text-primary"></i>
                            الرقم الوزاري
                        </th>
                        <th width="20%">
                            <i class="fas fa-user text-primary"></i>
                            الاسم الكامل
                        </th>
                        <th width="10%">
                            <i class="fas fa-graduation-cap text-success"></i>
                            علاوة التعليم
                        </th>
                        <th width="10%">
                            <i class="fas fa-cogs text-info"></i>
                            التجيير
                        </th>
                        <th width="10%">
                            <i class="fas fa-car text-warning"></i>
                            التنقلات
                        </th>
                        <th width="12%">
                            <i class="fas fa-user-tie text-secondary"></i>
                            العلاوة الإشرافية
                        </th>
                        <th width="10%">
                            <i class="fas fa-tools text-dark"></i>
                            علاوة فنية
                        </th>
                        <th width="11%">
                            <i class="fas fa-cogs text-dark"></i>
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {% for allowance in allowances %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ allowance.ministry_number|default:'-' }}</td>
                        <td>{{ allowance.employee.full_name }}</td>
                        <td class="text-center">
                            {% if allowance.education_allowance == 'yes' %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if allowance.adjustment_allowance == 'yes' %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if allowance.transportation_allowance == 'yes' %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if allowance.supervisory_allowance == 'yes' %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if allowance.technical_allowance == 'yes' %}
                                <span class="badge bg-success">نعم</span>
                            {% else %}
                                <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'ranks:employee_allowance_update' allowance.pk %}"
                                   class="btn btn-warning btn-sm" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'ranks:employee_allowance_delete' allowance.pk %}"
                                   class="btn btn-danger btn-sm" title="حذف"
                                   onclick="return confirm('هل أنت متأكد من حذف علاوات هذا الموظف؟')">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="9" class="text-center">
                            <div class="alert alert-info mb-0">
                                لا توجد علاوات. <a href="{% url 'ranks:employee_allowance_create' %}">إضافة علاوات جديدة</a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Get total count for calculations
    var totalCount = $('#allowancesTable tbody tr').length;
    var visibleCount = totalCount;

    // Search functionality
    $('#searchInput').on('keyup', function() {
        applyAllFilters();
    });

    // Filter functionality
    $('.filter-select').on('change', function() {
        applyAllFilters();
    });

    // Reset filters
    $('#resetFilters').on('click', function() {
        $('.filter-select').val('');
        $('#searchInput').val('');
        applyAllFilters();
    });

    // Apply all filters function
    function applyAllFilters() {
        var searchValue = $('#searchInput').val().toLowerCase();
        var educationFilter = $('#educationFilter').val();
        var adjustmentFilter = $('#adjustmentFilter').val();
        var transportationFilter = $('#transportationFilter').val();
        var supervisoryFilter = $('#supervisoryFilter').val();
        var technicalFilter = $('#technicalFilter').val();

        visibleCount = 0;

        $('#allowancesTable tbody tr').each(function() {
            var $row = $(this);
            var show = true;

            // Skip empty rows
            if ($row.find('td').length <= 1) {
                return;
            }

            // Search filter (ministry number and name)
            if (searchValue) {
                var ministryNumber = $row.find('td:eq(1)').text().toLowerCase();
                var employeeName = $row.find('td:eq(2)').text().toLowerCase();

                if (ministryNumber.indexOf(searchValue) === -1 &&
                    employeeName.indexOf(searchValue) === -1) {
                    show = false;
                }
            }

            // Education allowance filter
            if (educationFilter && show) {
                var educationText = $row.find('td:eq(3) .badge').text().trim();
                if (educationText !== educationFilter) {
                    show = false;
                }
            }

            // Adjustment allowance filter
            if (adjustmentFilter && show) {
                var adjustmentText = $row.find('td:eq(4) .badge').text().trim();
                if (adjustmentText !== adjustmentFilter) {
                    show = false;
                }
            }

            // Transportation allowance filter
            if (transportationFilter && show) {
                var transportationText = $row.find('td:eq(5) .badge').text().trim();
                if (transportationText !== transportationFilter) {
                    show = false;
                }
            }

            // Supervisory allowance filter
            if (supervisoryFilter && show) {
                var supervisoryText = $row.find('td:eq(6) .badge').text().trim();
                if (supervisoryText !== supervisoryFilter) {
                    show = false;
                }
            }

            // Technical allowance filter
            if (technicalFilter && show) {
                var technicalText = $row.find('td:eq(7) .badge').text().trim();
                if (technicalText !== technicalFilter) {
                    show = false;
                }
            }

            if (show) {
                $row.show();
                visibleCount++;
            } else {
                $row.hide();
            }
        });

        // Update counter
        $('.badge.bg-info').text(visibleCount + ' سجل');

        // Update filter info
        updateFilterInfo(searchValue, educationFilter, adjustmentFilter, transportationFilter, supervisoryFilter, technicalFilter, visibleCount);
    }

    // Update filter info function
    function updateFilterInfo(search, education, adjustment, transportation, supervisory, technical, count) {
        var filters = [];

        if (search) filters.push('البحث: "' + search + '"');
        if (education) filters.push('علاوة التعليم: ' + education);
        if (adjustment) filters.push('التجيير: ' + adjustment);
        if (transportation) filters.push('التنقلات: ' + transportation);
        if (supervisory) filters.push('العلاوة الإشرافية: ' + supervisory);
        if (technical) filters.push('علاوة فنية: ' + technical);

        var infoText = '';
        if (filters.length > 0) {
            infoText = '<i class="fas fa-filter text-primary"></i> مُطبق: ' + filters.join(' | ') + ' - النتائج: ' + count;
        } else {
            infoText = '<i class="fas fa-info-circle"></i> عرض جميع العلاوات - المجموع: ' + count;
        }

        $('#filterInfo').html(infoText);
    }

    // Initial filter application
    applyAllFilters();
});
</script>
{% endblock %}
