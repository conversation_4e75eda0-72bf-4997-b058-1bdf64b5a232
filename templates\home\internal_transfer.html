{% extends 'home/base.html' %}
{% load static %}

{% block title %}النقل الداخلي - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .loading-spinner {
        display: none;
        width: 1.5rem;
        height: 1.5rem;
        border: 3px solid rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        border-top-color: #009688;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    .form-section {
        display: none;
    }

    .form-section.active {
        display: block;
        animation: fadeIn 0.5s;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    .search-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0"><i class="fas fa-exchange-alt me-2"></i> طلب النقل الداخلي</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <h5 class="alert-heading"><i class="fas fa-info-circle me-2"></i> تعليمات هامة</h5>
                        <p>يرجى اتباع الخطوات التالية لتقديم طلب النقل الداخلي:</p>
                        <ol>
                            <li>أدخل الرقم الوزاري الخاص بك واضغط على زر "بحث".</li>
                            <li>تأكد من صحة البيانات التي تظهر تلقائياً.</li>
                            <li>اختر الأقسام التي ترغب بالنقل إليها حسب الأولوية.</li>
                            <li>أدخل سبب طلب النقل ومعلومات الاتصال.</li>
                            <li>اضغط على زر "تقديم الطلب" لإرسال الطلب.</li>
                        </ol>

                    </div>

                    <div class="d-flex justify-content-end mb-4">
                        <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editRequestModal">
                            <i class="fas fa-edit me-2"></i> تعديل طلب سابق
                        </button>
                    </div>

                    <form method="post" id="transferForm">
                        {% csrf_token %}

                        <!-- Search Section -->
                        <div class="search-section mb-4" id="searchSection">
                            <h5 class="border-bottom pb-2 mb-3">البحث عن الموظف</h5>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.ministry_number.id_for_label }}" class="form-label">{{ form.ministry_number.label }} <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        {{ form.ministry_number }}
                                    </div>
                                    <div class="form-text">أدخل الرقم الوزاري للبحث عن بياناتك</div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="national_id_input" class="form-label">الرقم الوطني <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="national_id_input" placeholder="أدخل الرقم الوطني">
                                    </div>
                                    <div class="form-text">أدخل الرقم الوطني للتحقق</div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="id_number_input" class="form-label">رقم الهوية <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="id_number_input" placeholder="أدخل رقم الهوية">
                                        <button class="btn btn-outline-secondary" type="button" id="mainHelpBtn">
                                            <i class="fas fa-question-circle"></i> مساعدة
                                        </button>
                                    </div>
                                    <div class="form-text">أدخل رقم الهوية للتحقق</div>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <button class="btn btn-primary w-100" type="button" id="searchBtn">
                                        <span class="search-text"><i class="fas fa-search me-1"></i> بحث والتحقق من البيانات</span>
                                        <div class="loading-spinner" id="searchSpinner"></div>
                                    </button>
                                    <div class="alert alert-danger d-none mt-2" id="searchError" role="alert"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Employee Information Section -->
                        <div class="form-section" id="employeeSection">
                            <h5 class="border-bottom pb-2 mb-3">معلومات الموظف</h5>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.employee_name.id_for_label }}" class="form-label">{{ form.employee_name.label }}</label>
                                    {{ form.employee_name }}
                                    {% if form.employee_name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.employee_name.errors }}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.employee_id.id_for_label }}" class="form-label">{{ form.employee_id.label }}</label>
                                    {{ form.employee_id }}
                                    {% if form.employee_id.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.employee_id.errors }}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.gender.id_for_label }}" class="form-label">{{ form.gender.label }}</label>
                                    {{ form.gender }}
                                    {% if form.gender.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.gender.errors }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <h5 class="border-bottom pb-2 mb-3 mt-4">معلومات الوظيفة الحالية</h5>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.current_department.id_for_label }}" class="form-label">{{ form.current_department.label }}</label>
                                    {{ form.current_department }}
                                    {% if form.current_department.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.current_department.errors }}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.hire_date.id_for_label }}" class="form-label">{{ form.hire_date.label }}</label>
                                    {{ form.hire_date }}
                                    {% if form.hire_date.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.hire_date.errors }}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.actual_service.id_for_label }}" class="form-label">{{ form.actual_service.label }}</label>
                                    {{ form.actual_service }}
                                    {% if form.actual_service.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.actual_service.errors }}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.last_position.id_for_label }}" class="form-label">{{ form.last_position.label }}</label>
                                    {{ form.last_position }}
                                    {% if form.last_position.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.last_position.errors }}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.qualification.id_for_label }}" class="form-label">{{ form.qualification.label }}</label>
                                    {{ form.qualification }}
                                    {% if form.qualification.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.qualification.errors }}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.specialization.id_for_label }}" class="form-label">{{ form.specialization.label }}</label>
                                    {{ form.specialization }}
                                    {% if form.specialization.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.specialization.errors }}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.last_rank.id_for_label }}" class="form-label">{{ form.last_rank.label }}</label>
                                    {{ form.last_rank }}
                                    {% if form.last_rank.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.last_rank.errors }}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-12 mb-3">
                                    <label for="{{ form.address.id_for_label }}" class="form-label">{{ form.address.label }}</label>
                                    {{ form.address }}
                                    {% if form.address.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.address.errors }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <h5 class="border-bottom pb-2 mb-3 mt-4">خيارات النقل</h5>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.first_choice.id_for_label }}" class="form-label">{{ form.first_choice.label }} <span class="text-danger">*</span></label>
                                    {{ form.first_choice }}
                                    {% if form.first_choice.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.first_choice.errors }}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.second_choice.id_for_label }}" class="form-label">{{ form.second_choice.label }}</label>
                                    {{ form.second_choice }}
                                    {% if form.second_choice.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.second_choice.errors }}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.third_choice.id_for_label }}" class="form-label">{{ form.third_choice.label }}</label>
                                    {{ form.third_choice }}
                                    {% if form.third_choice.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.third_choice.errors }}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-12 mb-3">
                                    <label for="{{ form.reason.id_for_label }}" class="form-label">{{ form.reason.label }} <span class="text-danger">*</span></label>
                                    {{ form.reason }}
                                    {% if form.reason.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.reason.errors }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <h5 class="border-bottom pb-2 mb-3 mt-4">معلومات الاتصال</h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.phone_number.id_for_label }}" class="form-label">{{ form.phone_number.label }} <span class="text-danger">*</span></label>
                                    {{ form.phone_number }}
                                    {% if form.phone_number.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.phone_number.errors }}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.email.id_for_label }}" class="form-label">{{ form.email.label }}</label>
                                    {{ form.email }}
                                    {% if form.email.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.email.errors }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="alert alert-warning mb-4">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                ملاحظة: سيتم تخزين طلبك وخيارات النقل وترتيبها حسب الخدمة الفعلية ولحين إجراء النقل الداخلي.
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i> تقديم الطلب
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
    <!-- Modal for searching existing transfer request -->
    <div class="modal fade" id="editRequestModal" tabindex="-1" aria-labelledby="editRequestModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="editRequestModalLabel"><i class="fas fa-search me-2"></i> البحث عن طلب نقل داخلي</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        أدخل الرقم الوزاري والرقم الوطني ورقم الهوية للبحث عن طلب النقل الداخلي الخاص بك وتعديله.
                    </div>

                    <div class="mb-3">
                        <label for="searchRequestInput" class="form-label">الرقم الوزاري <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchRequestInput" placeholder="أدخل الرقم الوزاري">
                        </div>
                        <div class="form-text">أدخل الرقم الوزاري للبحث عن طلب النقل الداخلي الخاص بك</div>
                    </div>

                    <div class="mb-3">
                        <label for="searchRequestNationalId" class="form-label">الرقم الوطني <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchRequestNationalId" placeholder="أدخل الرقم الوطني">
                        </div>
                        <div class="form-text">أدخل الرقم الوطني للتحقق من هويتك</div>
                    </div>

                    <div class="mb-3">
                        <label for="searchRequestIdNumber" class="form-label">رقم الهوية <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchRequestIdNumber" placeholder="أدخل رقم الهوية">
                            <button class="btn btn-outline-secondary" type="button" id="searchHelpBtn">
                                <i class="fas fa-question-circle"></i> مساعدة
                            </button>
                        </div>
                        <div class="form-text">أدخل رقم الهوية للتحقق من هويتك</div>
                    </div>

                    <div class="mb-3">
                        <button class="btn btn-primary w-100" type="button" id="searchRequestBtn">
                            <span class="search-text"><i class="fas fa-search me-1"></i> بحث</span>
                            <div class="loading-spinner" id="searchRequestSpinner" style="display: none;"></div>
                        </button>
                    </div>

                    <div class="alert alert-danger d-none" id="searchRequestError"></div>

                    <div class="search-result d-none" id="searchRequestResult">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title" id="resultEmployeeName"></h5>
                                <p class="card-text">تاريخ الطلب: <span id="resultCreatedAt"></span></p>
                            </div>
                            <div class="card-footer">
                                <a href="#" class="btn btn-primary w-100" id="editRequestLink">
                                    <i class="fas fa-edit me-2"></i> تعديل الطلب
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get the modal element
        var idHelpModalElement = document.getElementById('idHelpModal');

        // Get the help buttons
        var mainHelpBtn = document.getElementById('mainHelpBtn');
        var searchHelpBtn = document.getElementById('searchHelpBtn');

        // Function to show the modal
        function showHelpModal() {
            // Try different methods to show the modal
            try {
                // Method 1: Using Bootstrap 5
                if (typeof bootstrap !== 'undefined') {
                    var modal = new bootstrap.Modal(idHelpModalElement);
                    modal.show();
                    return;
                }

                // Method 2: Using jQuery (Bootstrap 4)
                if (typeof $ !== 'undefined') {
                    $(idHelpModalElement).modal('show');
                    return;
                }

                // Method 3: Manual display
                idHelpModalElement.style.display = 'block';
                idHelpModalElement.classList.add('show');
                document.body.classList.add('modal-open');

                // Add backdrop
                var backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop fade show';
                document.body.appendChild(backdrop);
            } catch (e) {
                console.error('Error showing modal:', e);
            }
        }

        // Function to close the help modal - defined globally
        window.closeHelpModal = function() {
            try {
                // Method 1: Using Bootstrap 5
                if (typeof bootstrap !== 'undefined') {
                    var modal = bootstrap.Modal.getInstance(idHelpModalElement);
                    if (modal) {
                        modal.hide();
                        return;
                    }
                }

                // Method 2: Using jQuery (Bootstrap 4)
                if (typeof $ !== 'undefined') {
                    $(idHelpModalElement).modal('hide');
                    return;
                }

                // Method 3: Manual hide
                idHelpModalElement.style.display = 'none';
                idHelpModalElement.classList.remove('show');
                document.body.classList.remove('modal-open');

                // Remove backdrop
                var backdrops = document.getElementsByClassName('modal-backdrop');
                if (backdrops.length > 0) {
                    for (var i = 0; i < backdrops.length; i++) {
                        backdrops[i].parentNode.removeChild(backdrops[i]);
                    }
                }
            } catch (e) {
                console.error('Error closing modal:', e);
            }
        }

        // Add click event listeners to help buttons
        if (mainHelpBtn) {
            mainHelpBtn.addEventListener('click', function(e) {
                e.preventDefault();
                showHelpModal();
            });
        }

        if (searchHelpBtn) {
            searchHelpBtn.addEventListener('click', function(e) {
                e.preventDefault();
                showHelpModal();
            });
        }
        // ===== SEARCH FUNCTIONALITY =====
        console.log('🚀 Initializing search functionality...');

        // Function to show error messages - SIMPLE AND DIRECT
        function showError(message) {
            console.log('🚨 SHOWING ERROR:', message);

            const errorElement = document.getElementById('searchError');
            const employeeSection = document.getElementById('employeeSection');

            // Hide employee section
            if (employeeSection) {
                employeeSection.classList.remove('active');
                console.log('✅ Employee section hidden');
            }

            // Show error message
            if (errorElement) {
                errorElement.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i><strong>خطأ:</strong> ${message}`;
                errorElement.classList.remove('d-none');
                errorElement.style.display = 'block';
                console.log('✅ Error message shown in UI');

                // Scroll to error
                setTimeout(() => {
                    errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 200);
            } else {
                console.error('❌ Error element not found!');
                alert('خطأ: ' + message);
            }
        }

        // Function to hide error messages
        function hideError() {
            const errorElement = document.getElementById('searchError');
            if (errorElement) {
                errorElement.classList.add('d-none');
                errorElement.style.display = 'none';
                errorElement.innerHTML = '';
                console.log('✅ Error message hidden');
            }
        }

        // Search button click handler - DIRECT AND SIMPLE
        const searchButton = document.getElementById('searchBtn');
        if (searchButton) {
            searchButton.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('🔍 SEARCH BUTTON CLICKED!');

                // Hide any previous errors
                hideError();

                // Get input elements
                const ministryInput = document.getElementById('ministry_number_input');
                const nationalIdInput = document.getElementById('national_id_input');
                const idNumberInput = document.getElementById('id_number_input');

                console.log('Input elements found:', {
                    ministry: !!ministryInput,
                    nationalId: !!nationalIdInput,
                    idNumber: !!idNumberInput
                });

                // Check if all inputs exist
                if (!ministryInput || !nationalIdInput || !idNumberInput) {
                    showError('لم يتم العثور على حقول الإدخال. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
                    return;
                }

                // Get values
                const ministryNumber = ministryInput.value.trim();
                const nationalId = nationalIdInput.value.trim();
                const idNumber = idNumberInput.value.trim();

                console.log('Input values:', { ministryNumber, nationalId, idNumber });

                // Validate inputs
                if (!ministryNumber) {
                    showError('يرجى إدخال الرقم الوزاري');
                    ministryInput.focus();
                    return;
                }

                if (!nationalId) {
                    showError('يرجى إدخال الرقم الوطني');
                    nationalIdInput.focus();
                    return;
                }

                if (!idNumber) {
                    showError('يرجى إدخال رقم الهوية');
                    idNumberInput.focus();
                    return;
                }

                // Show loading state
                const searchText = searchButton.querySelector('.search-text');
                const searchSpinner = document.getElementById('searchSpinner');

                if (searchText) searchText.style.display = 'none';
                if (searchSpinner) searchSpinner.style.display = 'inline-block';

                console.log('🔄 Loading state activated');

                // Make API request
                const searchUrl = `{% url 'home:search_employee' %}?ministry_number=${encodeURIComponent(ministryNumber)}&national_id=${encodeURIComponent(nationalId)}&id_number=${encodeURIComponent(idNumber)}`;
                console.log('🌐 API URL:', searchUrl);

                fetch(searchUrl)
                    .then(response => {
                        console.log('📡 Response status:', response.status);
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('📋 API Response:', data);

                        // Hide loading state
                        if (searchText) searchText.style.display = 'inline-block';
                        if (searchSpinner) searchSpinner.style.display = 'none';

                        if (data.success) {
                            console.log('✅ Employee found successfully');
                            fillEmployeeData(data.employee);

                            // Show employee section
                            const employeeSection = document.getElementById('employeeSection');
                            if (employeeSection) {
                                employeeSection.classList.add('active');
                                console.log('✅ Employee section shown');
                            }
                        } else {
                            console.log('❌ Search failed:', data.error);
                            showError(data.error);

                            // Handle existing transfer request
                            if (data.has_existing_transfer) {
                                handleExistingTransfer(data.transfer);
                            }
                        }
                    })
                    .catch(error => {
                        console.error('🚨 Network/API Error:', error);

                        // Hide loading state
                        if (searchText) searchText.style.display = 'inline-block';
                        if (searchSpinner) searchSpinner.style.display = 'none';

                        showError('حدث خطأ في الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت والمحاولة مرة أخرى.');
                    });
            });
        } else {
            console.error('❌ Search button not found!');
        }

        // Function to fill employee data in the form
        function fillEmployeeData(employee) {
            console.log('📝 FILLING EMPLOYEE DATA:', employee);

            try {
                // Basic employee information
                const fields = [
                    { id: 'id_employee_name', value: employee.full_name },
                    { id: 'id_employee_id', value: employee.national_id },
                    { id: 'id_current_department', value: employee.department },
                    { id: 'id_hire_date', value: employee.hire_date },
                    { id: 'id_actual_service', value: employee.actual_service },
                    { id: 'id_last_position', value: employee.last_position },
                    { id: 'id_qualification', value: employee.qualification },
                    { id: 'id_specialization', value: employee.specialization },
                    { id: 'id_last_rank', value: employee.last_rank },
                    { id: 'id_address', value: employee.address },
                    { id: 'id_phone_number', value: employee.phone_number }
                ];

                // Fill text fields
                fields.forEach(field => {
                    const element = document.getElementById(field.id);
                    if (element) {
                        element.value = field.value || '';
                        console.log(`✅ ${field.id} = "${field.value}"`);
                    } else {
                        console.warn(`⚠️ Field not found: ${field.id}`);
                    }
                });

                // Handle gender select
                const genderSelect = document.getElementById('id_gender');
                if (genderSelect && employee.gender) {
                    for (let i = 0; i < genderSelect.options.length; i++) {
                        if (genderSelect.options[i].value === employee.gender) {
                            genderSelect.selectedIndex = i;
                            console.log(`✅ Gender set to: ${employee.gender}`);
                            break;
                        }
                    }
                }

                console.log('✅ ALL EMPLOYEE DATA FILLED SUCCESSFULLY');

            } catch (error) {
                console.error('🚨 ERROR FILLING EMPLOYEE DATA:', error);
                showError('حدث خطأ أثناء ملء بيانات الموظف. يرجى المحاولة مرة أخرى.');
            }
        }

        // Function to handle existing transfer request
        function handleExistingTransfer(transfer) {
            console.log('📋 HANDLING EXISTING TRANSFER:', transfer);

            try {
                const modal = document.getElementById('editRequestModal');
                if (modal && typeof bootstrap !== 'undefined') {
                    const bootstrapModal = new bootstrap.Modal(modal);

                    // Fill transfer details
                    const employeeName = document.getElementById('resultEmployeeName');
                    const createdAt = document.getElementById('resultCreatedAt');
                    const editLink = document.getElementById('editRequestLink');

                    if (employeeName) employeeName.textContent = transfer.employee_name;
                    if (createdAt) createdAt.textContent = transfer.created_at;
                    if (editLink) editLink.href = `/internal-transfer/edit/${transfer.edit_token}/`;

                    // Show result section
                    const result = document.getElementById('searchRequestResult');
                    if (result) result.classList.remove('d-none');

                    bootstrapModal.show();
                    console.log('✅ Existing transfer modal shown');
                }
            } catch (error) {
                console.error('🚨 ERROR HANDLING EXISTING TRANSFER:', error);
            }
        }

        // Form submission validation
        const transferForm = document.getElementById('transferForm');
        if (transferForm) {
            transferForm.addEventListener('submit', function(e) {
                const employeeSection = document.getElementById('employeeSection');
                if (!employeeSection || !employeeSection.classList.contains('active')) {
                    e.preventDefault();
                    showError('يرجى البحث عن الموظف أولاً قبل تقديم الطلب');
                    return false;
                }
                console.log('✅ Form submission allowed');
            });
        }



        // Search for existing transfer request
        const searchRequestBtn = document.getElementById('searchRequestBtn');
        const searchRequestSpinner = document.getElementById('searchRequestSpinner');
        const searchRequestError = document.getElementById('searchRequestError');
        const searchRequestInput = document.getElementById('searchRequestInput');
        const searchRequestNationalId = document.getElementById('searchRequestNationalId');
        const searchRequestIdNumber = document.getElementById('searchRequestIdNumber');
        const searchRequestResult = document.getElementById('searchRequestResult');
        const resultEmployeeName = document.getElementById('resultEmployeeName');
        const resultCreatedAt = document.getElementById('resultCreatedAt');
        const editRequestLink = document.getElementById('editRequestLink');

        if (searchRequestBtn) {
            searchRequestBtn.addEventListener('click', function() {
                const ministryNumber = searchRequestInput.value.trim();
                const nationalId = searchRequestNationalId.value.trim();
                const idNumber = searchRequestIdNumber.value.trim();

                // Validate all required fields
                if (!ministryNumber) {
                    showSearchRequestError('الرجاء إدخال الرقم الوزاري');
                    return;
                }

                if (!nationalId) {
                    showSearchRequestError('الرجاء إدخال الرقم الوطني');
                    return;
                }

                if (!idNumber) {
                    showSearchRequestError('الرجاء إدخال رقم الهوية');
                    return;
                }

                // Show loading spinner
                searchRequestBtn.querySelector('.search-text').style.display = 'none';
                searchRequestSpinner.style.display = 'inline-block';
                searchRequestError.classList.add('d-none');
                searchRequestResult.classList.add('d-none');

                // Fetch transfer request data with verification parameters
                const searchUrl = `{% url 'home:search_transfer_request' %}?ministry_number=${ministryNumber}&national_id=${nationalId}&id_number=${idNumber}`;
                console.log('Search URL:', searchUrl);

                fetch(searchUrl)
                    .then(response => {
                        console.log('Response status:', response.status);
                        return response.json();
                    })
                    .then(data => {
                        // Hide loading spinner
                        searchRequestBtn.querySelector('.search-text').style.display = 'inline-block';
                        searchRequestSpinner.style.display = 'none';

                        console.log('Response data:', data);

                        if (data.success) {
                            // Show transfer request data
                            resultEmployeeName.textContent = data.transfer.employee_name;
                            resultCreatedAt.textContent = data.transfer.created_at;
                            editRequestLink.href = `{% url 'home:internal_transfer_edit' token='TOKEN_PLACEHOLDER' %}`.replace('TOKEN_PLACEHOLDER', data.transfer.edit_token);

                            // Show result section
                            searchRequestResult.classList.remove('d-none');
                        } else {
                            showSearchRequestError(data.error);
                        }
                    })
                    .catch(error => {
                        // Hide loading spinner
                        searchRequestBtn.querySelector('.search-text').style.display = 'inline-block';
                        searchRequestSpinner.style.display = 'none';

                        showSearchRequestError('حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.');
                        console.error('Error:', error);
                    });
            });
        }

        function showSearchRequestError(message) {
            searchRequestError.textContent = message;
            searchRequestError.classList.remove('d-none');
        }
    });
</script>
    <!-- Modal for ID Help -->
    <div class="modal fade" id="idHelpModal" tabindex="-1" aria-labelledby="idHelpModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title" id="idHelpModalLabel"><i class="fas fa-info-circle me-2"></i> مساعدة في إدخال رقم الهوية</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12 mb-4">
                            <h5 class="border-bottom pb-2 mb-3">كيفية العثور على رقم الهوية</h5>
                            <p>رقم الهوية هو الرقم المكون من عدة أرقام الموجود على الجانب الخلفي من بطاقة الهوية الأردنية. يرجى اتباع الخطوات التالية:</p>
                            <ol>
                                <li>قم بقلب بطاقة الهوية الأردنية إلى الجانب الخلفي.</li>
                                <li>ابحث عن الرقم الموجود في المنطقة المحددة باللون الأحمر في الصورة أدناه.</li>
                                <li>أدخل هذا الرقم بالكامل في حقل "رقم الهوية" دون ترك مسافات.</li>
                            </ol>
                        </div>
                        <div class="col-md-12 text-center">
                            <img src="/static/direct_images/jordan_id_back.jpg" alt="صورة توضيحية للهوية الأردنية من الخلف" class="img-fluid border rounded shadow-sm" style="max-width: 400px;">
                            <p class="mt-3 text-muted">صورة توضيحية للهوية الأردنية - المنطقة المحددة باللون الأحمر تشير إلى مكان رقم الهوية</p>
                        </div>
                        <div class="col-md-12 mt-4">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>ملاحظة هامة:</strong> يجب إدخال رقم الهوية بدقة كما هو مكتوب على البطاقة. أي خطأ في إدخال الرقم سيؤدي إلى فشل عملية التحقق.
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeHelpModal()">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Inline help modal (fallback) -->
    <div id="inlineHelpModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 9999;">
        <div style="position: relative; width: 80%; max-width: 800px; margin: 50px auto; background-color: white; border-radius: 5px; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.3);">
            <button onclick="document.getElementById('inlineHelpModal').style.display='none';" style="position: absolute; top: 10px; right: 10px; background: none; border: none; font-size: 20px; cursor: pointer;">&times;</button>
            <h3 style="color: #17a2b8; border-bottom: 1px solid #ddd; padding-bottom: 10px; margin-bottom: 20px;"><i class="fas fa-info-circle" style="margin-right: 10px;"></i> مساعدة في إدخال رقم الهوية</h3>
            <div>
                <h5 style="border-bottom: 1px solid #eee; padding-bottom: 10px; margin-bottom: 15px;">كيفية العثور على رقم الهوية</h5>
                <p>رقم الهوية هو الرقم المكون من عدة أرقام الموجود على الجانب الخلفي من بطاقة الهوية الأردنية. يرجى اتباع الخطوات التالية:</p>
                <ol>
                    <li>قم بقلب بطاقة الهوية الأردنية إلى الجانب الخلفي.</li>
                    <li>ابحث عن الرقم الموجود في المنطقة المحددة باللون الأحمر في الصورة أدناه.</li>
                    <li>أدخل هذا الرقم بالكامل في حقل "رقم الهوية" دون ترك مسافات.</li>
                </ol>
                <div style="text-align: center; margin: 20px 0;">
                    <img src="/static/direct_images/jordan_id_back.jpg" alt="صورة توضيحية للهوية الأردنية من الخلف" style="max-width: 400px; border: 1px solid #ddd; border-radius: 5px; box-shadow: 0 0 5px rgba(0,0,0,0.1);">
                    <p style="margin-top: 10px; color: #6c757d;">صورة توضيحية للهوية الأردنية - المنطقة المحددة باللون الأحمر تشير إلى مكان رقم الهوية</p>
                </div>
                <div style="background-color: #fff3cd; border: 1px solid #ffeeba; color: #856404; padding: 15px; border-radius: 5px; margin-top: 20px;">
                    <strong>ملاحظة هامة:</strong> يجب إدخال رقم الهوية بدقة كما هو مكتوب على البطاقة. أي خطأ في إدخال الرقم سيؤدي إلى فشل عملية التحقق.
                </div>
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button onclick="document.getElementById('inlineHelpModal').style.display='none';" style="background-color: #6c757d; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer;">إغلاق</button>
            </div>
        </div>
    </div>
{% endblock %}
