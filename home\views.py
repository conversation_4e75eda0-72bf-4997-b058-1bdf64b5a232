from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse, HttpResponse
from django.db.models import Q, Count
from django.utils import timezone
from django.core.paginator import Paginator
from django.conf import settings as django_settings
import pandas as pd
import io
import pytz
import time
import json
import os
import mimetypes
from datetime import date
from .forms import InternalTransferForm, ApprovedFormForm, TechnicalTransferForm
from .models import InternalTransfer, SystemSettings, ApprovedForm, TechnicalTransfer
from employees.models import Employee
from employment.models import Employment, Position, EmployeePosition, Department
from ranks.models import EmployeeRank


def format_job_title(position):
    """
    Format job title with correct prefix based on position name.

    Args:
        position (str): The position name

    Returns:
        str: Formatted job title with correct prefix
    """
    print(f"FORMAT JOB TITLE - Input position: '{position}'")

    if not position:
        print("FORMAT JOB TITLE - Empty position, returning 'الموظف'")
        return "الموظف"

    # List of positions that need "ال" prefix - ONLY معلم and حارس need "ال"
    positions_with_al = ["معلم", "حارس"]

    # First, remove any existing "ال" prefix
    if position.startswith("ال"):
        position_without_al = position[2:]
        print(f"FORMAT JOB TITLE - Removed 'ال' prefix: '{position_without_al}'")
    else:
        position_without_al = position
        print(f"FORMAT JOB TITLE - No 'ال' prefix to remove: '{position_without_al}'")

    # Check if the position is in the list that needs "ال"
    for pos in positions_with_al:
        if position_without_al == pos or position_without_al.startswith(pos + " "):
            # This position needs "ال"
            result = "ال" + position_without_al
            print(f"FORMAT JOB TITLE - Position needs 'ال', returning: '{result}'")
            return result

    # If we get here, the position doesn't need "ال"
    print(f"FORMAT JOB TITLE - Position doesn't need 'ال', returning: '{position_without_al}'")
    return position_without_al

def convert_to_local_timezone(obj):
    """
    تحويل حقول التاريخ في الكائن إلى المنطقة الزمنية المحلية لنظام التشغيل
    """
    # استخدام المنطقة الزمنية المحددة في إعدادات Django (Asia/Amman)
    # هذا يضمن استخدام نفس المنطقة الزمنية بغض النظر عن نظام التشغيل
    local_timezone = timezone.get_current_timezone()
    print(f"Using Django timezone: {local_timezone}")

    # طباعة معلومات عن المنطقة الزمنية
    now = timezone.now()
    utc_now = now.astimezone(pytz.UTC)
    local_now = now.astimezone(local_timezone)
    print(f"Current UTC time: {utc_now}")
    print(f"Current local time: {local_now}")
    print(f"Offset from UTC: {local_now.utcoffset()}")

    # تحويل التواريخ إلى المنطقة الزمنية المحلية
    if hasattr(obj, 'created_at') and obj.created_at and obj.created_at.tzinfo is not None:
        original_created_at = obj.created_at
        obj.created_at = obj.created_at.astimezone(local_timezone)
        print(f"Converted created_at from {original_created_at} to local time: {obj.created_at}")

    if hasattr(obj, 'updated_at') and obj.updated_at and obj.updated_at.tzinfo is not None:
        original_updated_at = obj.updated_at
        obj.updated_at = obj.updated_at.astimezone(local_timezone)
        print(f"Converted updated_at from {original_updated_at} to local time: {obj.updated_at}")

    return obj

def home_view(request):
    """View for the home page"""
    return render(request, 'home/home.html')

def important_links_view(request):
    """View for the important links page"""
    links = [
        {
            'name': 'وزارة التربية والتعليم',
            'url': 'https://moe.gov.jo/',
            'favicon_url': 'https://moe.gov.jo/favicon.ico'
        },
        {
            'name': 'منصة الموظفين',
            'url': 'https://emp.moe.gov.jo/public/P01_Home/',
            'favicon_url': 'https://emp.moe.gov.jo/favicon.ico'
        },
        {
            'name': 'منصة تدريب المعلمين',
            'url': 'https://teachers.gov.jo/',
            'favicon_url': 'https://teachers.gov.jo/favicon.ico'
        },
        {
            'name': 'OpenEmis',
            'url': 'https://emis.moe.gov.jo/openemis-core/',
            'favicon_url': 'https://emis.moe.gov.jo/favicon.ico'
        },
        {
            'name': 'BtecEmis JO',
            'url': 'https://apps.moe.gov.jo/btec/btecemis/public/Home/',
            'favicon_url': 'https://apps.moe.gov.jo/favicon.ico'
        },
        {
            'name': 'التعليم الإضافي',
            'url': 'https://apps.moe.gov.jo/apps/subteachers/',
            'favicon_url': 'https://apps.moe.gov.jo/favicon.ico'
        },
        {
            'name': 'الوصف الوظيفي',
            'url': 'https://apps.moe.gov.jo/files/actcards/',
            'favicon_url': 'https://apps.moe.gov.jo/favicon.ico'
        },
        {
            'name': 'نماذج تقييم الأداء السنوي',
            'url': 'https://apps.moe.gov.jo/files/actcards/%D9%86%D9%85%D8%A7%D8%B0%D8%AC%20%D8%AA%D9%82%D9%8A%D9%8A%D9%85%20%20%D8%A7%D9%84%D8%A7%D8%AF%D8%A7%D8%A1%20%D8%A7%D9%84%D9%85%D8%B9%D8%AA%D9%85%D8%AF%D8%A9%20%20%D9%84%D9%84%D8%B9%D8%A7%D9%85%202025/',
            'favicon_url': 'https://apps.moe.gov.jo/favicon.ico'
        },
    ]
    return render(request, 'home/important_links.html', {'links': links})

def search_employee(request):
    """API view to search for an employee by ministry number, national ID, and ID number"""
    ministry_number = request.GET.get('ministry_number', '')
    national_id = request.GET.get('national_id', '')
    id_number = request.GET.get('id_number', '')

    print(f"Searching for employee with ministry number: {ministry_number}, national ID: {national_id}, ID number: {id_number}")

    if not ministry_number:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوزاري'})

    # Remove requirement for national_id and id_number - allow employees without these
    # if not national_id:
    #     return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوطني'})

    # if not id_number:
    #     return JsonResponse({'success': False, 'error': 'الرجاء إدخال رقم الهوية'})

    try:
        # Find employee by ministry number
        employee = Employee.objects.get(ministry_number=ministry_number)
        print(f"Found employee: {employee.full_name}")

        # Verify national ID
        if employee.national_id and employee.national_id != national_id:
            return JsonResponse({
                'success': False,
                'error': 'البيانات المدخلة غير صحيحة. يرجى التأكد من صحة الرقم الوطني المدخل والمحاولة مرة أخرى.'
            })

        # Check if employee has identification data with ID number
        from employment.models import EmployeeIdentification, BtecTeacher
        try:
            identification = EmployeeIdentification.objects.get(employee=employee)

            # Verify ID number
            if identification.id_number != id_number:
                return JsonResponse({
                    'success': False,
                    'error': 'البيانات المدخلة غير صحيحة. يرجى التأكد من صحة رقم الهوية المدخل والمحاولة مرة أخرى.'
                })

            if not identification.id_number:
                return JsonResponse({
                    'success': False,
                    'error': 'البيانات المدخلة غير مكتملة في النظام. يرجى مراجعة قسم شؤون الموظفين لاستكمال البيانات المطلوبة.'
                })
        except EmployeeIdentification.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': 'البيانات المدخلة غير مكتملة في النظام. يرجى مراجعة قسم شؤون الموظفين لاستكمال البيانات المطلوبة.'
            })

        # Check if employee is a BTEC teacher
        btec_teacher = BtecTeacher.objects.filter(employee=employee).first()
        if btec_teacher:
            return JsonResponse({
                'success': False,
                'error': f'الموظف {employee.full_name} مضاف كمعلم BTEC في حقل {btec_teacher.field.name}. لا يجوز لمعلمي BTEC تقديم طلبات النقل الداخلي.'
            })

        # Check if employee already has a transfer request in the current year
        from datetime import date
        current_year = date.today().year
        existing_transfer = InternalTransfer.objects.filter(
            ministry_number=ministry_number,
            created_at__year=current_year
        ).order_by('-created_at').first()

        if existing_transfer:
            print(f"Employee already has a transfer request in {current_year}: {existing_transfer.id}")
            return JsonResponse({
                'success': False,
                'error': 'لديك طلب نقل داخلي مسبق خلال هذه السنة. لا يمكنك تقديم طلب جديد، ولكن يمكنك تعديل طلبك الحالي.',
                'has_existing_transfer': True,
                'transfer': {
                    'id': existing_transfer.id,
                    'employee_name': existing_transfer.employee_name,
                    'edit_token': existing_transfer.edit_token,
                    'created_at': existing_transfer.created_at.strftime('%Y-%m-%d')
                }
            })

        # Get latest position
        latest_position = employee.get_latest_position()
        print(f"Latest position: {latest_position}")

        # Get latest rank
        latest_rank = EmployeeRank.objects.filter(employee=employee).order_by('-date_obtained').first()
        latest_rank_name = latest_rank.rank_type.name if latest_rank else '-'
        print(f"Latest rank: {latest_rank_name}")

        # Calculate actual service
        actual_service = "غير محدد"
        if employee.hire_date:
            today = date.today()
            years = today.year - employee.hire_date.year - ((today.month, today.day) < (employee.hire_date.month, employee.hire_date.day))
            months = today.month - employee.hire_date.month
            if months < 0:
                months += 12
            actual_service = f"{years} سنة و {months} شهر"
            print(f"Actual service: {actual_service}")

        # Prepare response data
        response_data = {
            'success': True,
            'employee': {
                'ministry_number': employee.ministry_number,
                'full_name': employee.full_name,
                'national_id': employee.national_id,
                'gender': employee.gender,
                'qualification': employee.qualification,
                'specialization': employee.specialization,
                'hire_date': employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else '',
                'actual_service': actual_service,
                'department': employee.school,
                'last_position': latest_position,
                'last_rank': latest_rank_name,
                'address': employee.address,
                'phone_number': employee.phone_number
            }
        }

        print(f"Response data: {response_data}")

        # Return employee data
        return JsonResponse(response_data)
    except Employee.DoesNotExist:
        print(f"Employee not found with ministry number: {ministry_number}")
        return JsonResponse({'success': False, 'error': 'البيانات المدخلة غير صحيحة. يرجى التأكد من صحة البيانات المدخلة والمحاولة مرة أخرى.'})
    except Exception as e:
        print(f"Error searching for employee: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})

def internal_transfer_view(request):
    """View for the internal transfer page"""
    # Check if internal transfer is enabled
    settings = SystemSettings.get_settings()
    print(f"Internal transfer view called. Enabled: {settings.internal_transfer_enabled}")

    if not settings.internal_transfer_enabled:
        print(f"Internal transfer is disabled. Showing closed page with message: {settings.internal_transfer_message}")
        return render(request, 'home/internal_transfer_closed_new.html', {
            'message': settings.internal_transfer_message
        })

    print("Internal transfer is enabled. Showing form page.")
    if request.method == 'POST':
        form = InternalTransferForm(request.POST)
        if form.is_valid():
            transfer = form.save()
            messages.success(request, _(
                'تم تقديم طلب النقل الداخلي بنجاح. سيتم تخزين طلبك وخيارات النقل وترتيبها حسب الخدمة الفعلية. '
                f'يمكنك تعديل طلبك من خلال الرابط: /internal-transfer/edit/{transfer.edit_token}/'
            ))
            return redirect('home:internal_transfer_success', token=transfer.edit_token)
    else:
        form = InternalTransferForm()

    return render(request, 'home/internal_transfer.html', {'form': form})

def internal_transfer_success(request, token):
    """View for the internal transfer success page"""
    transfer = get_object_or_404(InternalTransfer, edit_token=token)
    return render(request, 'home/internal_transfer_success.html', {'transfer': transfer})

def internal_transfer_edit(request, token):
    """View for editing an internal transfer request"""
    # Check if internal transfer is enabled
    settings = SystemSettings.get_settings()
    if not settings.internal_transfer_enabled:
        return render(request, 'home/internal_transfer_closed_new.html', {
            'message': settings.internal_transfer_message
        })

    transfer = get_object_or_404(InternalTransfer, edit_token=token)

    # Check if employee has identification data with ID number
    try:
        employee = Employee.objects.get(ministry_number=transfer.ministry_number)
        try:
            from employment.models import EmployeeIdentification, BtecTeacher
            identification = EmployeeIdentification.objects.get(employee=employee)
            if not identification.id_number:
                messages.error(request, f'الموظف {employee.full_name} ليس لديه رقم هوية مسجل في النظام. يرجى إضافة رقم الهوية أولاً.')
                return redirect('home:internal_transfer_success', token=transfer.edit_token)
        except EmployeeIdentification.DoesNotExist:
            messages.error(request, f'الموظف {employee.full_name} ليس لديه بيانات تعريفية مسجلة في النظام. يرجى إضافة البيانات التعريفية أولاً.')
            return redirect('home:internal_transfer_success', token=transfer.edit_token)

        # Check if employee is a BTEC teacher
        btec_teacher = BtecTeacher.objects.filter(employee=employee).first()
        if btec_teacher:
            messages.error(request, f'الموظف {employee.full_name} مضاف كمعلم BTEC في حقل {btec_teacher.field.name}. لا يجوز لمعلمي BTEC تقديم طلبات النقل الداخلي.')
            return redirect('home:internal_transfer_success', token=transfer.edit_token)
    except Employee.DoesNotExist:
        # Si no se encuentra el empleado, permitir la edición de todos modos
        pass

    if request.method == 'POST':
        # Verificar el número nacional y el número de identificación si se proporcionan
        national_id = request.POST.get('national_id', '')
        id_number = request.POST.get('id_number', '')

        if national_id and id_number:
            try:
                employee = Employee.objects.get(ministry_number=transfer.ministry_number)

                # Verificar el número nacional
                if employee.national_id and employee.national_id != national_id:
                    messages.error(request, f'الرقم الوطني المدخل ({national_id}) لا يتطابق مع الرقم الوطني للموظف ({employee.national_id})')
                    form = InternalTransferForm(instance=transfer)
                    return render(request, 'home/internal_transfer_edit.html', {'form': form, 'transfer': transfer})

                # Verificar el número de identificación
                try:
                    identification = EmployeeIdentification.objects.get(employee=employee)
                    if identification.id_number != id_number:
                        messages.error(request, f'رقم الهوية المدخل ({id_number}) لا يتطابق مع رقم الهوية المسجل للموظف ({identification.id_number})')
                        form = InternalTransferForm(instance=transfer)
                        return render(request, 'home/internal_transfer_edit.html', {'form': form, 'transfer': transfer})
                except EmployeeIdentification.DoesNotExist:
                    messages.error(request, f'الموظف {employee.full_name} ليس لديه بيانات تعريفية مسجلة في النظام.')
                    form = InternalTransferForm(instance=transfer)
                    return render(request, 'home/internal_transfer_edit.html', {'form': form, 'transfer': transfer})
            except Employee.DoesNotExist:
                # Si no se encuentra el empleado, permitir la edición de todos modos
                pass

        form = InternalTransferForm(request.POST, instance=transfer)
        if form.is_valid():
            form.save()
            messages.success(request, _('تم تحديث طلب النقل الداخلي بنجاح.'))
            return redirect('home:internal_transfer_success', token=transfer.edit_token)
    else:
        form = InternalTransferForm(instance=transfer)

    return render(request, 'home/internal_transfer_edit.html', {'form': form, 'transfer': transfer, 'verification_required': True})

def search_transfer_request(request):
    """API view to search for an internal transfer request by ministry number, national ID, and ID number"""
    ministry_number = request.GET.get('ministry_number', '')
    national_id = request.GET.get('national_id', '')
    id_number = request.GET.get('id_number', '')

    print(f"Searching for transfer request with ministry number: {ministry_number}, national ID: {national_id}, ID number: {id_number}")

    if not ministry_number:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوزاري'})

    if not national_id:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوطني'})

    if not id_number:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال رقم الهوية'})

    try:
        # Verify employee identity
        try:
            employee = Employee.objects.get(ministry_number=ministry_number)

            # Verify national ID
            if employee.national_id and employee.national_id != national_id:
                return JsonResponse({
                    'success': False,
                    'error': f'الرقم الوطني المدخل ({national_id}) لا يتطابق مع الرقم الوطني للموظف ({employee.national_id})'
                })

            # Verify ID number
            try:
                from employment.models import EmployeeIdentification, BtecTeacher
                identification = EmployeeIdentification.objects.get(employee=employee)

                if identification.id_number != id_number:
                    return JsonResponse({
                        'success': False,
                        'error': f'رقم الهوية المدخل ({id_number}) لا يتطابق مع رقم الهوية المسجل للموظف ({identification.id_number})'
                    })
            except EmployeeIdentification.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'error': f'الموظف {employee.full_name} ليس لديه بيانات تعريفية مسجلة في النظام.'
                })

            # Check if employee is a BTEC teacher
            btec_teacher = BtecTeacher.objects.filter(employee=employee).first()
            if btec_teacher:
                return JsonResponse({
                    'success': False,
                    'error': f'الموظف {employee.full_name} مضاف كمعلم BTEC في حقل {btec_teacher.field.name}. لا يجوز لمعلمي BTEC تقديم طلبات النقل الداخلي.'
                })
        except Employee.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري'})

        # Find transfer request by ministry number
        transfer = InternalTransfer.objects.filter(ministry_number=ministry_number).order_by('-created_at').first()

        if not transfer:
            return JsonResponse({
                'success': False,
                'error': 'لم يتم العثور على طلب نقل داخلي لهذا الرقم الوزاري'
            })

        print(f"Found transfer request for: {transfer.employee_name}")

        # Return transfer data with edit token
        return JsonResponse({
            'success': True,
            'transfer': {
                'id': transfer.id,
                'employee_name': transfer.employee_name,
                'edit_token': transfer.edit_token,
                'created_at': transfer.created_at.strftime('%Y-%m-%d')
            }
        })
    except Exception as e:
        print(f"Error searching for transfer request: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})

def about_view(request):
    """View for the about page"""
    return render(request, 'home/about.html')


def search_view(request):
    """View for the search page"""
    query = request.GET.get('q', '')
    results = []

    if query:
        # تقسيم النص المدخل إلى كلمات للبحث عن كل كلمة بشكل منفصل
        search_terms = query.split()

        # إنشاء استعلام للبحث في الموظفين
        employee_query = Q()
        for term in search_terms:
            employee_query |= Q(full_name__icontains=term)
            employee_query |= Q(ministry_number__icontains=term)
            employee_query |= Q(national_id__icontains=term)
            employee_query |= Q(school__icontains=term)
            # إضافة حقول أخرى للبحث
            # البحث في العلاقات
            employee_query |= Q(positions__position__name__icontains=term)  # البحث في المسميات الوظيفية
            employee_query |= Q(specialization__icontains=term)
            employee_query |= Q(qualification__icontains=term)
            # لا يوجد حقل notes في نموذج Employee

        # البحث في الموظفين
        employees = Employee.objects.filter(employee_query).distinct()[:20]

        # إنشاء استعلام للبحث في الأقسام
        department_query = Q()
        for term in search_terms:
            department_query |= Q(name__icontains=term)
            department_query |= Q(description__icontains=term)
            department_query |= Q(workplace__icontains=term)

        # البحث في الأقسام
        departments = Department.objects.filter(department_query).distinct()[:10]

        # إنشاء استعلام للبحث في المسميات الوظيفية
        position_query = Q()
        for term in search_terms:
            position_query |= Q(name__icontains=term)
            position_query |= Q(description__icontains=term) if hasattr(Position, 'description') else Q()

        # البحث في المسميات الوظيفية
        positions = Position.objects.filter(position_query).distinct()[:10]

        # إنشاء استعلام للبحث في طلبات النقل الداخلي
        transfer_query = Q()
        for term in search_terms:
            transfer_query |= Q(employee_name__icontains=term)
            transfer_query |= Q(ministry_number__icontains=term)
            transfer_query |= Q(current_department__icontains=term)
            transfer_query |= Q(first_choice__icontains=term)
            transfer_query |= Q(second_choice__icontains=term)
            transfer_query |= Q(third_choice__icontains=term)
            transfer_query |= Q(specialization__icontains=term)
            transfer_query |= Q(qualification__icontains=term)
            # البحث في الملاحظات إذا كانت موجودة
            if hasattr(InternalTransfer, 'notes'):
                transfer_query |= Q(notes__icontains=term)

        # البحث في طلبات النقل الداخلي
        transfers = InternalTransfer.objects.filter(transfer_query).distinct()[:10]

        # إنشاء استعلام للبحث في النماذج المعتمدة
        form_query = Q()
        for term in search_terms:
            form_query |= Q(title__icontains=term)
            form_query |= Q(description__icontains=term)

        # البحث في النماذج المعتمدة
        forms = ApprovedForm.objects.filter(form_query).distinct()[:10]

        # Combine results
        results = {
            'employees': employees,
            'departments': departments,
            'positions': positions,
            'transfers': transfers,
            'forms': forms
        }

    return render(request, 'home/search_results.html', {
        'query': query,
        'results': results
    })


@login_required
def approved_forms_list(request):
    """View for listing approved forms"""
    # Get all active approved forms
    forms = ApprovedForm.objects.filter(is_active=True).order_by('order', '-created_at')

    return render(request, 'home/approved_forms_list.html', {
        'forms': forms,
        'title': 'النماذج المعتمدة'
    })


@login_required
def approved_forms_admin(request):
    """View for managing approved forms (admin)"""
    # Get all approved forms
    forms = ApprovedForm.objects.all().order_by('order', '-created_at')

    # Paginate the results
    paginator = Paginator(forms, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'home/approved_forms_admin.html', {
        'forms': page_obj,
        'title': 'إدارة النماذج المعتمدة'
    })


@login_required
def approved_form_create(request):
    """View for creating a new approved form"""
    if request.method == 'POST':
        form = ApprovedFormForm(request.POST, request.FILES)
        if form.is_valid():
            approved_form = form.save()
            messages.success(request, 'تم إضافة النموذج بنجاح.')
            return redirect('home:approved_forms_admin')
    else:
        form = ApprovedFormForm()

    return render(request, 'home/approved_form_form.html', {
        'form': form,
        'title': 'إضافة نموذج جديد'
    })


@login_required
def approved_form_update(request, pk):
    """View for updating an approved form"""
    approved_form = get_object_or_404(ApprovedForm, pk=pk)

    if request.method == 'POST':
        form = ApprovedFormForm(request.POST, request.FILES, instance=approved_form)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث النموذج بنجاح.')
            return redirect('home:approved_forms_admin')
    else:
        form = ApprovedFormForm(instance=approved_form)

    return render(request, 'home/approved_form_form.html', {
        'form': form,
        'approved_form': approved_form,
        'title': 'تعديل النموذج'
    })


@login_required
def approved_form_delete(request, pk):
    """View for deleting an approved form"""
    approved_form = get_object_or_404(ApprovedForm, pk=pk)

    if request.method == 'POST':
        approved_form.delete()
        messages.success(request, 'تم حذف النموذج بنجاح.')
        return redirect('home:approved_forms_admin')

    return render(request, 'home/approved_form_confirm_delete.html', {
        'approved_form': approved_form,
        'title': 'حذف النموذج'
    })


def approved_form_download(request, pk):
    """View for downloading an approved form"""
    approved_form = get_object_or_404(ApprovedForm, pk=pk)

    # Get the file path
    file_path = approved_form.file.path

    # Check if file exists
    if os.path.exists(file_path):
        # Determine the content type
        content_type, encoding = mimetypes.guess_type(file_path)
        if content_type is None:
            content_type = 'application/octet-stream'

        # Open the file
        with open(file_path, 'rb') as file:
            response = HttpResponse(file.read(), content_type=content_type)

            # Set the Content-Disposition header to force download
            response['Content-Disposition'] = f'attachment; filename="{os.path.basename(file_path)}"'

            return response

    # If file doesn't exist, return 404
    return HttpResponse('الملف غير موجود', status=404)

@login_required
def internal_transfer_list_view(request):
    """View for listing internal transfer requests (for admin users)"""
    # Get filters
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')
    year_filter = request.GET.get('year', '')
    gender_filter = request.GET.get('gender', '')
    specialization_filter = request.GET.get('specialization', '')

    # Filter transfers
    transfers = InternalTransfer.objects.all().order_by('-created_at')

    if search_query:
        transfers = transfers.filter(
            Q(employee_name__icontains=search_query) |
            Q(ministry_number__icontains=search_query) |
            Q(employee_id__icontains=search_query) |
            Q(current_department__icontains=search_query) |
            Q(first_choice__icontains=search_query)
        )

    if status_filter:
        transfers = transfers.filter(status=status_filter)

    if year_filter:
        transfers = transfers.filter(created_at__year=year_filter)

    if gender_filter:
        transfers = transfers.filter(gender=gender_filter)

    if specialization_filter:
        transfers = transfers.filter(specialization=specialization_filter)

    # Get unique years, genders and specializations for filters
    from django.db.models import Count
    years = InternalTransfer.objects.dates('created_at', 'year').order_by('-created_at')
    years = [date.year for date in years]

    genders = InternalTransfer.objects.values('gender').annotate(count=Count('id')).order_by('gender')

    specializations = InternalTransfer.objects.exclude(specialization__isnull=True).exclude(specialization='').values('specialization').annotate(count=Count('id')).order_by('specialization')

    # Update actual service for each transfer from Employee model
    from datetime import date
    for transfer in transfers:
        try:
            employee = Employee.objects.get(ministry_number=transfer.ministry_number)
            if employee.hire_date:
                today = date.today()
                years_service = today.year - employee.hire_date.year - ((today.month, today.day) < (employee.hire_date.month, employee.hire_date.day))
                months_service = today.month - employee.hire_date.month
                if months_service < 0:
                    months_service += 12
                transfer.actual_service = f"{years_service} سنة و {months_service} شهر"
        except Employee.DoesNotExist:
            # Keep the existing actual_service if employee not found
            pass

    # Get system settings
    settings = SystemSettings.get_settings()

    # Calculate counts by status
    pending_count = InternalTransfer.objects.filter(status='pending').count()
    approved_count = InternalTransfer.objects.filter(status='approved').count()
    rejected_count = InternalTransfer.objects.filter(status='rejected').count()

    # Get all school departments for the new department selection
    departments = Department.objects.filter(workplace='school').order_by('name')

    return render(request, 'home/internal_transfer_list.html', {
        'transfers': transfers,
        'search_query': search_query,
        'years': years,
        'genders': genders,
        'specializations': specializations,
        'year_filter': year_filter,
        'gender_filter': gender_filter,
        'specialization_filter': specialization_filter,
        'status_filter': status_filter,
        'settings': settings,
        'pending_count': pending_count,
        'approved_count': approved_count,
        'rejected_count': rejected_count,
        'departments': departments
    })

@login_required
def internal_transfer_detail_view(request, pk):
    """View for viewing internal transfer request details (for admin users)"""
    transfer = get_object_or_404(InternalTransfer, pk=pk)

    if request.method == 'POST':
        status = request.POST.get('status')
        notes = request.POST.get('notes')

        if status:
            transfer.status = status
        if notes is not None:
            transfer.notes = notes

        transfer.save()
        messages.success(request, _('تم تحديث حالة طلب النقل الداخلي بنجاح.'))
        return redirect('home:internal_transfer_list')

    # تحويل التواريخ إلى المنطقة الزمنية المحلية لنظام التشغيل
    transfer = convert_to_local_timezone(transfer)

    # Inicializar las propiedades para evitar errores
    transfer.actual_service_detailed = None
    transfer.latest_rank_details = None

    # Imprimir información de depuración
    print(f"\n\nDEBUG INFO FOR TRANSFER ID {pk}:")
    print(f"Ministry Number: {transfer.ministry_number}")
    print(f"Employee Name: {transfer.employee_name}")
    print(f"Last Rank: {transfer.last_rank}")
    print(f"Actual Service: {transfer.actual_service}")

    # الحصول على الخدمة الفعلية وآخر رتبة من النظام
    try:
        from employees.models import Employee
        from leaves.models import Leave, LeaveType
        from ranks.models import EmployeeRank, RankType
        from datetime import date
        import sys

        # Buscar el empleado por número ministerial
        employee = Employee.objects.filter(ministry_number=transfer.ministry_number).first()

        if employee:
            print(f"Found employee: {employee.full_name} (ID: {employee.id})")

            # Verificar si hay rangos para este empleado
            all_ranks = list(EmployeeRank.objects.filter(employee=employee).order_by('-date_obtained'))
            print(f"Found {len(all_ranks)} ranks for employee")

            # Obtener el último rango
            if all_ranks:
                latest_rank = all_ranks[0]
                print(f"Latest rank: {latest_rank}")

                # Crear un diccionario con los detalles de la clasificación
                transfer.latest_rank_details = {
                    'name': latest_rank.rank_type.name,
                    'date': latest_rank.date_obtained.strftime('%Y-%m-%d'),
                    'degree': getattr(latest_rank, 'degree', "غير محدد")
                }
                print(f"Set latest_rank_details: {transfer.latest_rank_details}")
            else:
                print("No ranks found for employee")
                # Usar el valor existente en transfer.last_rank si está disponible
                if transfer.last_rank and transfer.last_rank != '-':
                    transfer.latest_rank_details = {
                        'name': transfer.last_rank,
                        'date': 'غير متوفر',
                        'degree': 'غير متوفر'
                    }

            # حساب الخدمة الفعلية إذا كان تاريخ التعيين متوفر
            if employee.hire_date:
                print(f"Employee hire date: {employee.hire_date}")

                # حساب إجمالي الأيام بين تاريخ التعيين واليوم
                today = date.today()
                total_days = (today - employee.hire_date).days

                # الحصول على الإجازات بدون راتب
                unpaid_leave_type = LeaveType.objects.filter(name=LeaveType.UNPAID).first()
                unpaid_leave_days = 0

                if unpaid_leave_type:
                    unpaid_leaves = Leave.objects.filter(
                        employee=employee,
                        leave_type=unpaid_leave_type,
                        status='approved'
                    )

                    # حساب مجموع أيام الإجازات بدون راتب
                    for leave in unpaid_leaves:
                        unpaid_leave_days += leave.days_count

                # حساب أيام الخدمة الفعلية
                actual_service_days = total_days - unpaid_leave_days

                # تحويل إلى سنوات وشهور وأيام
                years = actual_service_days // 365
                remaining_days = actual_service_days % 365
                months = remaining_days // 30
                days = remaining_days % 30

                # تخزين الخدمة الفعلية المحسوبة
                transfer.actual_service_detailed = f"{years} سنة, {months} شهر, {days} يوم"
                print(f"Set actual_service_detailed: {transfer.actual_service_detailed}")
            else:
                print("Employee has no hire date")
                # Usar el valor existente en transfer.actual_service si está disponible
                if transfer.actual_service and transfer.actual_service != '-':
                    transfer.actual_service_detailed = transfer.actual_service
                else:
                    transfer.actual_service_detailed = "غير متوفر"
        else:
            print(f"No employee found with ministry number: {transfer.ministry_number}")
            # Usar los valores existentes en transfer si están disponibles
            if transfer.actual_service and transfer.actual_service != '-':
                transfer.actual_service_detailed = transfer.actual_service
            else:
                transfer.actual_service_detailed = "غير متوفر"

            if transfer.last_rank and transfer.last_rank != '-':
                transfer.latest_rank_details = {
                    'name': transfer.last_rank,
                    'date': 'غير متوفر',
                    'degree': 'غير متوفر'
                }
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()

        # Usar los valores existentes en transfer si están disponibles
        if transfer.actual_service and transfer.actual_service != '-':
            transfer.actual_service_detailed = transfer.actual_service
        else:
            transfer.actual_service_detailed = "غير متوفر"

        if transfer.last_rank and transfer.last_rank != '-':
            transfer.latest_rank_details = {
                'name': transfer.last_rank,
                'date': 'غير متوفر',
                'degree': 'غير متوفر'
            }

    return render(request, 'home/internal_transfer_detail.html', {'transfer': transfer})

@login_required
def internal_transfer_delete(request, pk):
    """View for deleting an internal transfer request"""
    print(f"Delete view called for transfer ID: {pk}, Method: {request.method}")

    # Get the transfer object
    try:
        transfer = get_object_or_404(InternalTransfer, pk=pk)
    except:
        messages.error(request, 'طلب النقل الداخلي غير موجود.')
        return redirect('home:internal_transfer_list')

    # Store information for logging
    ministry_number = transfer.ministry_number
    employee_name = transfer.employee_name

    print(f"Found transfer for deletion: {employee_name} (ID: {pk}, Ministry #: {ministry_number})")

    # If this is a POST request, process the deletion
    if request.method == 'POST':
        try:
            # Delete the transfer
            transfer.delete()
            print(f"Successfully deleted transfer request for {employee_name} (Ministry #: {ministry_number})")
            messages.success(request, f'تم حذف طلب النقل الداخلي للموظف {employee_name} بنجاح.')
            return redirect('home:internal_transfer_list')
        except Exception as e:
            print(f"Error deleting transfer request with ID {pk}: {str(e)}")
            messages.error(request, 'حدث خطأ أثناء محاولة حذف طلب النقل الداخلي.')
            return redirect('home:internal_transfer_list')

    # إذا كان هذا طلب GET، قم بعرض صفحة تأكيد الحذف
    # تحويل التواريخ إلى المنطقة الزمنية المحلية لنظام التشغيل
    transfer = convert_to_local_timezone(transfer)

    return render(request, 'home/internal_transfer_confirm_delete.html', {'transfer': transfer})

@login_required
def toggle_internal_transfer(request):
    """View for toggling internal transfer page"""
    settings = SystemSettings.get_settings()

    print(f"Toggle internal transfer view called. Method: {request.method}")
    print(f"Current settings: Enabled={settings.internal_transfer_enabled}, Message={settings.internal_transfer_message}")

    if request.method == 'POST':
        action = request.POST.get('action')
        message = request.POST.get('message', '')

        print(f"POST data: action={action}, message={message}")

        if action == 'enable':
            # Force update to True
            settings.internal_transfer_enabled = True
            settings.save()
            print(f"Enabled internal transfer. New settings: Enabled={settings.internal_transfer_enabled}")
            messages.success(request, _('تم تفعيل صفحة النقل الداخلي بنجاح.'))
        elif action == 'disable':
            # Force update to False
            settings.internal_transfer_enabled = False
            if message:
                settings.internal_transfer_message = message
            settings.save()
            print(f"Disabled internal transfer. New settings: Enabled={settings.internal_transfer_enabled}, Message={settings.internal_transfer_message}")
            messages.success(request, _('تم تعطيل صفحة النقل الداخلي بنجاح.'))

        # Verify the update was successful
        settings.refresh_from_db()
        print(f"After save: Enabled={settings.internal_transfer_enabled}, Message={settings.internal_transfer_message}")

    return redirect('home:internal_transfer_list')

@login_required
def enable_internal_transfer(request):
    """View for enabling internal transfer page"""
    settings = SystemSettings.get_settings()
    settings.internal_transfer_enabled = True
    settings.save()
    messages.success(request, _('تم تفعيل صفحة النقل الداخلي بنجاح.'))
    return redirect('home:internal_transfer_list')

@login_required
def disable_internal_transfer(request):
    """View for disabling internal transfer page"""
    settings = SystemSettings.get_settings()
    settings.internal_transfer_enabled = False
    message = request.POST.get('message', '')
    if message:
        settings.internal_transfer_message = message
    settings.save()
    messages.success(request, _('تم تعطيل صفحة النقل الداخلي بنجاح.'))
    return redirect('home:internal_transfer_list')

@login_required
def dashboard_view(request):
    """View for the dashboard page with employee statistics"""
    # Get employee statistics by gender
    gender_stats = Employee.objects.values('gender').annotate(count=Count('id')).order_by('gender')
    gender_data = {
        'labels': [item['gender'] for item in gender_stats],
        'data': [item['count'] for item in gender_stats],
        'label_names': {
            'male': 'ذكر',
            'female': 'أنثى'
        }
    }

    # Get employee statistics by qualification
    qualification_stats = Employee.objects.values('qualification').annotate(count=Count('id')).order_by('-count')[:10]
    qualification_data = {
        'labels': [item['qualification'] for item in qualification_stats],
        'data': [item['count'] for item in qualification_stats]
    }

    # Get employee statistics by position
    position_stats = EmployeePosition.objects.values('position__name').annotate(count=Count('id')).order_by('-count')[:10]
    position_data = {
        'labels': [item['position__name'] for item in position_stats],
        'data': [item['count'] for item in position_stats]
    }

    context = {
        'gender_data': json.dumps(gender_data),
        'qualification_data': json.dumps(qualification_data),
        'position_data': json.dumps(position_data),
        'total_employees': Employee.objects.count(),
        'total_positions': Position.objects.count(),
        'total_departments': Employment.objects.values('department').distinct().count()
    }

    return render(request, 'home/dashboard.html', context)

@login_required
def analytics_dashboard_view(request):
    """View for the analytics dashboard with advanced statistics and visualizations"""
    # Import Count again to ensure it's available in this function scope
    from django.db.models import Count

    # Get employee statistics by gender
    gender_stats = Employee.objects.values('gender').annotate(count=Count('id')).order_by('gender')
    gender_data = {
        'labels': [item['gender'] for item in gender_stats],
        'data': [item['count'] for item in gender_stats],
        'label_names': {
            'male': 'ذكر',
            'female': 'أنثى'
        }
    }

    # Get employee statistics by qualification
    qualification_stats = Employee.objects.values('qualification').annotate(count=Count('id')).order_by('-count')[:10]
    qualification_data = {
        'labels': [item['qualification'] for item in qualification_stats],
        'data': [item['count'] for item in qualification_stats]
    }

    # Get employee statistics by position
    position_stats = EmployeePosition.objects.values('position__name').annotate(count=Count('id')).order_by('-count')[:10]
    position_data = {
        'labels': [item['position__name'] for item in position_stats],
        'data': [item['count'] for item in position_stats]
    }

    # Get employee growth data (simulated for now)
    from datetime import datetime, timedelta
    today = datetime.now()
    months = []
    counts = []

    # Simulate growth data for the last 6 months
    total_employees = Employee.objects.count()
    base_count = max(total_employees - 50, 0)  # Start with a reasonable base

    for i in range(5, -1, -1):
        month_date = today - timedelta(days=30*i)
        month_name = month_date.strftime('%B')

        # Translate month names to Arabic
        arabic_months = {
            'January': 'يناير', 'February': 'فبراير', 'March': 'مارس',
            'April': 'أبريل', 'May': 'مايو', 'June': 'يونيو',
            'July': 'يوليو', 'August': 'أغسطس', 'September': 'سبتمبر',
            'October': 'أكتوبر', 'November': 'نوفمبر', 'December': 'ديسمبر'
        }

        months.append(arabic_months.get(month_name, month_name))

        # Simulate growth pattern
        if i == 0:
            counts.append(total_employees)
        else:
            counts.append(base_count + int((total_employees - base_count) * (5-i)/5))

    growth_data = {
        'labels': months,
        'data': counts
    }

    # Calculate employee growth percentage
    employee_growth = 5  # Default value
    if len(counts) >= 2:
        if counts[0] > 0:
            employee_growth = round(((counts[-1] - counts[0]) / counts[0]) * 100, 1)

    # Get departments with employee counts
    departments = Employment.objects.values('department__name').annotate(
        count=Count('employee', distinct=True)
    ).order_by('-count')

    total_count = sum(dept['count'] for dept in departments)

    # Calculate gender distribution by department
    departments_data = []
    for dept in departments:
        if not dept['department__name']:
            continue

        dept_name = dept['department__name']
        dept_count = dept['count']

        # Get gender counts for this department
        gender_counts = Employment.objects.filter(
            department__name=dept_name
        ).values(
            'employee__gender'
        ).annotate(
            count=Count('employee', distinct=True)
        )

        male_count = 0
        female_count = 0

        for gender_count in gender_counts:
            if gender_count['employee__gender'] == 'male':
                male_count = gender_count['count']
            elif gender_count['employee__gender'] == 'female':
                female_count = gender_count['count']

        # Calculate percentages
        percentage = round((dept_count / total_count) * 100, 1) if total_count > 0 else 0
        male_percentage = round((male_count / dept_count) * 100) if dept_count > 0 else 0
        female_percentage = round((female_count / dept_count) * 100) if dept_count > 0 else 0

        departments_data.append({
            'name': dept_name,
            'count': dept_count,
            'percentage': percentage,
            'male_count': male_count,
            'female_count': female_count,
            'male_percentage': male_percentage,
            'female_percentage': female_percentage
        })

    # Get active leaves count
    from django.utils import timezone
    from leaves.models import Leave
    today = timezone.now().date()
    active_leaves = Leave.objects.filter(
        start_date__lte=today,
        end_date__gte=today,
        status='approved'
    ).count()

    # Calculate leave percentage
    total_employees = Employee.objects.count()
    leave_percentage = round((active_leaves / total_employees) * 100, 1) if total_employees > 0 else 0

    # Count active departments (departments with employees)
    departments_with_employees = Department.objects.filter(
        id__in=Employment.objects.values('department').distinct()
    ).count()

    # Count positions with employees
    positions_with_employees = Employment.objects.values('position').distinct().count()

    context = {
        'gender_data': json.dumps(gender_data),
        'qualification_data': json.dumps(qualification_data),
        'position_data': json.dumps(position_data),
        'growth_data': json.dumps(growth_data),
        'total_employees': Employee.objects.count(),
        'total_positions': Position.objects.count(),
        'total_departments': Department.objects.count(),
        'departments_data': departments_data,
        'active_leaves': active_leaves,
        'leave_percentage': leave_percentage,
        'employee_growth': employee_growth,
        'departments_with_employees': departments_with_employees,
        'positions_with_employees': positions_with_employees
    }

    return render(request, 'home/analytics_dashboard.html', context)

@login_required
def delete_all_internal_transfers(request):
    """Delete all internal transfer requests"""
    if request.method == 'POST':
        try:
            # Get confirmation from the form
            confirmation = request.POST.get('confirmation', '')

            if confirmation != 'DELETE_ALL':
                messages.error(request, 'يرجى كتابة "DELETE_ALL" للتأكيد على حذف جميع الطلبات.')
                return redirect('home:internal_transfer_list')

            # Count transfers before deletion
            count = InternalTransfer.objects.count()

            # Delete all transfers
            InternalTransfer.objects.all().delete()

            messages.success(request, f'تم حذف جميع طلبات النقل الداخلي بنجاح. عدد الطلبات المحذوفة: {count}')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء محاولة حذف جميع طلبات النقل الداخلي: {str(e)}')

    return redirect('home:internal_transfer_list')

@login_required
def print_transfer_letters(request):
    """View for printing internal transfer letters"""
    # Get current year
    current_year = timezone.now().year

    # Get transfers for current year
    transfers = InternalTransfer.objects.filter(
        created_at__year=current_year
    ).order_by('specialization', '-actual_service')

    # Get all school departments
    departments = Department.objects.filter(workplace='school').order_by('name')

    # Update actual service for each transfer from Employee model
    from datetime import date
    for transfer in transfers:
        try:
            employee = Employee.objects.get(ministry_number=transfer.ministry_number)
            if employee.hire_date:
                today = date.today()
                years_service = today.year - employee.hire_date.year - ((today.month, today.day) < (employee.hire_date.month, employee.hire_date.day))
                months_service = today.month - employee.hire_date.month
                if months_service < 0:
                    months_service += 12
                transfer.actual_service = f"{years_service} سنة و {months_service} شهر"
        except Employee.DoesNotExist:
            # Keep the existing actual_service if employee not found
            pass

    # Handle AJAX request to save new department
    if request.method == 'POST' and (request.headers.get('X-Requested-With') == 'XMLHttpRequest' or request.POST.get('X-Requested-With') == 'XMLHttpRequest'):
        transfer_id = request.POST.get('transfer_id')
        department_id = request.POST.get('department_id')

        if not transfer_id or not department_id:
            return JsonResponse({
                'success': False,
                'error': 'معرف النقل أو معرف القسم غير صالح'
            })

        try:
            transfer = InternalTransfer.objects.get(id=transfer_id)
            department = Department.objects.get(id=department_id)
            notes = request.POST.get('notes', '')

            # Update the notes in the database
            transfer.notes = notes
            transfer.save()

            # Store in the session
            if 'transfer_departments' not in request.session:
                request.session['transfer_departments'] = {}

            request.session['transfer_departments'][str(transfer_id)] = {
                'department_id': department_id,
                'department_name': department.name,
                'notes': notes
            }
            request.session.modified = True

            # Log the saved department for debugging
            print(f"Saved department for transfer {transfer_id}: {department.name}")
            print(f"Saved notes for transfer {transfer_id}: {notes}")
            print(f"Session data: {request.session['transfer_departments']}")

            # Return JSON response for AJAX requests
            return JsonResponse({
                'success': True,
                'message': f'تم تعيين {transfer.employee_name} إلى {department.name} بنجاح',
                'department_name': department.name,
                'notes': notes
            })
        except InternalTransfer.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': f'لم يتم العثور على طلب النقل بالمعرف {transfer_id}'
            })
        except Department.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': f'لم يتم العثور على القسم بالمعرف {department_id}'
            })
        except Exception as e:
            # Log the error for debugging
            import traceback
            print(f"Error saving department: {str(e)}")
            print(traceback.format_exc())

            return JsonResponse({
                'success': False,
                'error': f'حدث خطأ غير متوقع: {str(e)}'
            })

    # Add new_department from session
    if 'transfer_departments' in request.session:
        for transfer in transfers:
            transfer_id_str = str(transfer.id)
            if transfer_id_str in request.session['transfer_departments']:
                # Add new_department as a dynamic attribute
                transfer.new_department = request.session['transfer_departments'][transfer_id_str]['department_name']

                # Notes are already in the database, but we'll update from session if available
                if 'notes' in request.session['transfer_departments'][transfer_id_str]:
                    session_notes = request.session['transfer_departments'][transfer_id_str]['notes']
                    # Only update if session notes are different from database notes
                    if session_notes != transfer.notes:
                        transfer.notes = session_notes
                        # Save to database to ensure consistency
                        transfer.save()

    # Render the print template
    return render(request, 'home/internal_transfer_print.html', {
        'transfers': transfers,
        'departments': departments,
        'current_year': current_year,
        'title': 'طباعة كتب النقل الداخلي'
    })

@login_required
def print_single_transfer_letter(request, transfer_id):
    """View for printing a single internal transfer letter"""
    # Get the transfer
    transfer = get_object_or_404(InternalTransfer, id=transfer_id)

    # Get new department from session
    new_department = ""
    notes = ""
    if 'transfer_departments' in request.session and str(transfer_id) in request.session['transfer_departments']:
        new_department = request.session['transfer_departments'][str(transfer_id)]['department_name']
        if 'notes' in request.session['transfer_departments'][str(transfer_id)]:
            notes = request.session['transfer_departments'][str(transfer_id)]['notes']

    # Get employee information for position handling
    try:
        employee = Employee.objects.get(ministry_number=transfer.ministry_number)
        position = employee.get_latest_position() or ""

        # Format job title
        job_title = format_job_title(position)

        print(f"Single Transfer Letter - Original Position: {position}")
        print(f"Single Transfer Letter - Formatted Job Title: {job_title}")
    except Employee.DoesNotExist:
        job_title = "الموظف"
        print("Single Transfer Letter - Employee not found")

    # Render the single transfer letter template
    return render(request, 'home/single_transfer_letter.html', {
        'transfer': transfer,
        'new_department': new_department,
        'notes': notes,
        'job_title': job_title
    })

@login_required
def print_transfer_summary(request):
    """View for printing internal transfer summary"""
    # Get current year
    current_year = timezone.now().year

    # Get transfers for current year
    transfers = InternalTransfer.objects.filter(
        created_at__year=current_year
    ).order_by('specialization', '-actual_service')

    # Add new_department from session
    if 'transfer_departments' in request.session:
        for transfer in transfers:
            transfer_id_str = str(transfer.id)
            if transfer_id_str in request.session['transfer_departments']:
                # Add new_department as a dynamic attribute
                transfer.new_department = request.session['transfer_departments'][transfer_id_str]['department_name']

                # Notes are already in the database, but we'll update from session if available
                if 'notes' in request.session['transfer_departments'][transfer_id_str]:
                    session_notes = request.session['transfer_departments'][transfer_id_str]['notes']
                    # Only update if session notes are different from database notes
                    if session_notes != transfer.notes:
                        transfer.notes = session_notes
                        # Save to database to ensure consistency
                        transfer.save()

    # Filter transfers to only include those with a new department
    transfers_with_new_department = [t for t in transfers if hasattr(t, 'new_department')]

    # Get today's date
    from datetime import date
    today_date = date.today().strftime('%Y-%m-%d')

    # Render the summary template
    return render(request, 'home/internal_transfer_summary.html', {
        'transfers': transfers_with_new_department,
        'current_year': current_year,
        'today_date': today_date
    })

@login_required
def export_internal_transfers_to_excel(request):
    """Export internal transfers to Excel"""
    # Get filters
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')
    year_filter = request.GET.get('year', '')
    gender_filter = request.GET.get('gender', '')
    specialization_filter = request.GET.get('specialization', '')

    # Filter transfers
    transfers = InternalTransfer.objects.all().order_by('-created_at')

    if search_query:
        transfers = transfers.filter(
            Q(employee_name__icontains=search_query) |
            Q(ministry_number__icontains=search_query) |
            Q(employee_id__icontains=search_query) |
            Q(current_department__icontains=search_query) |
            Q(first_choice__icontains=search_query)
        )

    if status_filter:
        transfers = transfers.filter(status=status_filter)

    if year_filter:
        transfers = transfers.filter(created_at__year=year_filter)

    if gender_filter:
        transfers = transfers.filter(gender=gender_filter)

    if specialization_filter:
        transfers = transfers.filter(specialization=specialization_filter)

    # Update actual service for each transfer from Employee model
    from datetime import date
    for transfer in transfers:
        try:
            employee = Employee.objects.get(ministry_number=transfer.ministry_number)
            if employee.hire_date:
                today = date.today()
                years_service = today.year - employee.hire_date.year - ((today.month, today.day) < (employee.hire_date.month, employee.hire_date.day))
                months_service = today.month - employee.hire_date.month
                if months_service < 0:
                    months_service += 12
                transfer.actual_service = f"{years_service} سنة و {months_service} شهر"
        except Employee.DoesNotExist:
            # Keep the existing actual_service if employee not found
            pass

    # Create DataFrame
    data = []
    for transfer in transfers:
        status_text = 'قيد الانتظار'
        if transfer.status == 'approved':
            status_text = 'تمت الموافقة'
        elif transfer.status == 'rejected':
            status_text = 'مرفوض'

        gender_text = 'ذكر' if transfer.gender == 'male' else 'أنثى'

        data.append({
            'اسم الموظف': transfer.employee_name,
            'الرقم الوزاري': transfer.ministry_number,
            'الرقم الوطني': transfer.employee_id,
            'الخدمة الفعلية': transfer.actual_service or '',
            'القسم الحالي': transfer.current_department,
            'الخيار الأول': transfer.first_choice,
            'الخيار الثاني': transfer.second_choice or '',
            'الخيار الثالث': transfer.third_choice or '',
            'تاريخ الطلب': transfer.created_at.strftime('%Y-%m-%d'),
            'الحالة': status_text,
            'الجنس': gender_text,
            'المؤهل العلمي': transfer.qualification or '',
            'التخصص': transfer.specialization or '',
            'رقم الهاتف': transfer.phone_number,
            'البريد الإلكتروني': transfer.email or '',
            'سبب النقل': transfer.reason,
            'ملاحظات': transfer.notes or ''
        })

    # Create DataFrame
    df = pd.DataFrame(data)

    # Create Excel file
    output = io.BytesIO()
    try:
        # Try to use xlsxwriter engine
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, index=False, sheet_name='طلبات النقل الداخلي')
            worksheet = writer.sheets['طلبات النقل الداخلي']

            # Set RTL direction
            worksheet.right_to_left()

            # Auto-fit columns
            for i, col in enumerate(df.columns):
                column_width = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.set_column(i, i, column_width)
    except (ImportError, ModuleNotFoundError):
        # Fallback to openpyxl engine if xlsxwriter is not available
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='طلبات النقل الداخلي')
            # Note: openpyxl doesn't support RTL direction and column width adjustment directly
            # We'll need to inform the user about this limitation

    # Prepare response
    output.seek(0)
    response = HttpResponse(
        output.read(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = 'attachment; filename=internal_transfers.xlsx'

    # Add a message if we used the fallback engine
    try:
        import xlsxwriter
    except ImportError:
        messages.warning(
            request,
            _('تم تصدير البيانات بنجاح، ولكن قد لا يتم عرض النص بشكل صحيح من اليمين إلى اليسار. '
              'يرجى تثبيت مكتبة xlsxwriter للحصول على أفضل النتائج.')
        )

    return response


def technical_transfer_view(request):
    """View for the technical transfer page"""
    if request.method == 'POST':
        form = TechnicalTransferForm(request.POST)
        if form.is_valid():
            # Check if employee already has a technical transfer this year
            ministry_number = form.cleaned_data['ministry_number']
            current_year = timezone.now().year

            existing_transfer = TechnicalTransfer.objects.filter(
                ministry_number=ministry_number,
                created_at__year=current_year
            ).exists()

            if existing_transfer:
                messages.error(request, _('لا يمكن إضافة كتاب نقل فني جديد. يوجد كتاب نقل فني سابق للموظف خلال السنة الحالية.'))
            else:
                technical_transfer = form.save()
                messages.success(request, _('تم حفظ النقل الفني بنجاح.'))
                return redirect('home:technical_transfer_list')
    else:
        form = TechnicalTransferForm()

    return render(request, 'home/technical_transfer.html', {'form': form})


@login_required
def technical_transfer_delete(request, pk):
    """View for deleting a technical transfer"""
    technical_transfer = get_object_or_404(TechnicalTransfer, pk=pk)

    if request.method == 'POST':
        technical_transfer.delete()
        messages.success(request, _('تم حذف النقل الفني بنجاح.'))
        return redirect('home:technical_transfer_list')

    return render(request, 'home/technical_transfer_delete.html', {
        'transfer': technical_transfer
    })


@login_required
def technical_transfer_list(request):
    """View for listing technical transfers"""
    # Get all technical transfers
    technical_transfers = TechnicalTransfer.objects.all().order_by('-created_at')

    return render(request, 'home/technical_transfer_list.html', {
        'technical_transfers': technical_transfers,
        'title': 'قائمة النقل الفني'
    })


def technical_transfer_search(request):
    """API view to search for an employee by ministry number for technical transfer"""
    ministry_number = request.GET.get('ministry_number', '')

    if not ministry_number:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوزاري'})

    try:
        # Find employee by ministry number
        employee = Employee.objects.get(ministry_number=ministry_number)

        # Get latest position
        latest_position = employee.get_latest_position()

        # Get current department
        current_department = employee.school or "غير محدد"

        # Prepare response data
        response_data = {
            'success': True,
            'employee': {
                'ministry_number': employee.ministry_number,
                'full_name': employee.full_name,
                'current_department': current_department,
                'last_position': latest_position
            }
        }

        # Return employee data
        return JsonResponse(response_data)
    except Employee.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


def technical_transfer_print(request, pk):
    """View for printing a technical transfer letter"""
    technical_transfer = get_object_or_404(TechnicalTransfer, pk=pk)

    # Get department information
    new_department = technical_transfer.new_department

    # Get employee information
    try:
        employee = Employee.objects.get(ministry_number=technical_transfer.ministry_number)
        position = employee.get_latest_position() or ""

        # Format job title
        job_title = format_job_title(position)

        print(f"Technical Transfer - Original Position: {position}")
        print(f"Technical Transfer - Formatted Job Title: {job_title}")
    except Employee.DoesNotExist:
        job_title = "الموظف"
        print("Technical Transfer - Employee not found")

    return render(request, 'home/technical_transfer_letter.html', {
        'transfer': technical_transfer,
        'new_department': new_department,
        'job_title': job_title
    })


@login_required
def leave_balance_inquiry(request):
    """View for leave balance inquiry page"""
    return render(request, 'home/leave_balance_inquiry.html', {
        'title': 'رصيد الإجازات'
    })


@login_required
def get_employee_leave_balance(request):
    """AJAX view to get employee leave balance by ministry number or name"""
    search_term = request.GET.get('search_term', '').strip()

    if not search_term:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوزاري أو الاسم'})

    try:
        from leaves.models import LeaveBalance, LeaveType
        from employment.models import Employment

        # البحث عن الموظف بالرقم الوزاري أو الاسم
        employee = None

        # محاولة البحث بالرقم الوزاري أولاً
        try:
            employee = Employee.objects.get(ministry_number=search_term)
        except Employee.DoesNotExist:
            # البحث بالاسم
            employees = Employee.objects.filter(full_name__icontains=search_term)
            if employees.count() == 1:
                employee = employees.first()
            elif employees.count() > 1:
                # إذا كان هناك أكثر من موظف بنفس الاسم
                employee_list = []
                for emp in employees:
                    employee_list.append({
                        'ministry_number': emp.ministry_number,
                        'full_name': emp.full_name,
                        'school': emp.school
                    })
                return JsonResponse({
                    'success': False,
                    'error': 'يوجد أكثر من موظف بهذا الاسم. يرجى استخدام الرقم الوزاري للبحث الدقيق.',
                    'multiple_employees': employee_list
                })
            else:
                return JsonResponse({'success': False, 'error': 'لم يتم العثور على الموظف'})

        # التحقق من أن الموظف يعمل في المديرية
        directorate_employment = Employment.objects.filter(
            employee=employee,
            is_current=True,
            department__workplace='directorate'
        ).first()

        if not directorate_employment:
            return JsonResponse({
                'success': False,
                'error': 'هذا الموظف لا يعمل في المديرية حالياً'
            })

        # الحصول على السنة الحالية
        current_year = timezone.now().year

        # الحصول على أرصدة الإجازات للموظف
        leave_balances = LeaveBalance.objects.filter(
            employee=employee,
            year=current_year
        ).select_related('leave_type')

        # تنظيم البيانات - استخدام نفس طريقة تقرير موظفي المديرية
        balance_data = []
        leave_types = ['annual', 'sick', 'casual']  # الأنواع الرئيسية للإجازات

        for leave_type_name in leave_types:
            try:
                leave_type = LeaveType.objects.get(name=leave_type_name)
                balance = leave_balances.filter(leave_type=leave_type).first()

                if balance:
                    # حساب الإجازات المستخدمة من جدول الإجازات مباشرة (نفس طريقة تقرير موظفي المديرية)
                    from django.db.models import Sum
                    used_leaves = Leave.objects.filter(
                        employee=employee,
                        leave_type=leave_type,
                        start_date__year=current_year,
                        status='approved'
                    ).aggregate(total_days=Sum('days_count'))

                    used_days = used_leaves['total_days'] or 0
                    remaining = balance.initial_balance - used_days

                    balance_data.append({
                        'type_name': leave_type.get_name_display(),
                        'initial_balance': balance.initial_balance,
                        'used_balance': used_days,
                        'remaining_balance': remaining if remaining >= 0 else 0
                    })
                else:
                    # إذا لم يوجد رصيد، أضف صف بقيم صفر
                    balance_data.append({
                        'type_name': leave_type.get_name_display(),
                        'initial_balance': 0,
                        'used_balance': 0,
                        'remaining_balance': 0
                    })
            except LeaveType.DoesNotExist:
                # إذا لم يوجد نوع الإجازة، أضف صف بقيم صفر مع اسم افتراضي
                type_names = {
                    'annual': 'سنوية',
                    'sick': 'مرضية',
                    'casual': 'عرضية'
                }
                balance_data.append({
                    'type_name': type_names.get(leave_type_name, leave_type_name),
                    'initial_balance': 0,
                    'used_balance': 0,
                    'remaining_balance': 0
                })

        # إضافة تاريخ الاستعلام بالتقويم الميلادي
        from datetime import datetime
        current_date = datetime.now().strftime('%Y-%m-%d')

        # البحث عن آخر تحديث للإجازات
        last_leave_update = None
        if leave_balances.exists():
            # البحث عن آخر تحديث في أرصدة الإجازات
            latest_balance = leave_balances.order_by('-updated_at').first()
            if latest_balance:
                last_leave_update = latest_balance.updated_at.strftime('%Y-%m-%d')

        # إذا لم يوجد تحديث في الأرصدة، ابحث عن آخر إجازة
        if not last_leave_update:
            from leaves.models import Leave
            latest_leave = Leave.objects.filter(
                employee=employee,
                status='approved'
            ).order_by('-updated_at').first()
            if latest_leave:
                last_leave_update = latest_leave.updated_at.strftime('%Y-%m-%d')

        return JsonResponse({
            'success': True,
            'employee': {
                'ministry_number': employee.ministry_number,
                'full_name': employee.full_name,
                'department': directorate_employment.department.name,
                'year': current_year,
                'inquiry_date': current_date,
                'last_leave_update': last_leave_update or 'لا يوجد'
            },
            'balances': balance_data
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': f'حدث خطأ: {str(e)}'})