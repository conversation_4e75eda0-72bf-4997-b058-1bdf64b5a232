from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q
from django.utils import timezone
import pandas as pd
from io import BytesIO
from datetime import date, datetime
# ReportLab imports
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os
import arabic_reshaper
from bidi.algorithm import get_display
from openpyxl.styles import Font, PatternFill, Border, Side
from .models import Employee, AnnualReport, Penalty
from .forms import EmployeeForm, AnnualReportForm, PenaltyForm, EmployeeImportForm
from employment.models import Department, Position, AppointmentType
from leaves.models import Leave, LeaveType
from accounts.models import User
from ranks.models import RankType, EmployeeRank
from file_management.models import FileMovement
from system_logs.models import SystemLog

@login_required
def dashboard(request):
    # Get counts for dashboard
    employee_count = Employee.objects.count()
    department_count = Department.objects.count()
    user_count = User.objects.count()

    # Get active leaves
    try:
        from leaves.models import Leave
        today = timezone.now().date()
        active_leaves_count = Leave.objects.filter(
            start_date__lte=today,
            end_date__gte=today,
            status='approved'
        ).count()
    except (ImportError, Exception):
        active_leaves_count = 0

    # Get counts for new sections
    rank_count = EmployeeRank.objects.count()

    # Get file count from File model
    try:
        from file_management.models import File
        file_count = File.objects.count()
    except (ImportError, Exception):
        file_count = 0

    # Get unpaid leave count
    try:
        from leaves.models import Leave, LeaveType
        unpaid_leave_type = LeaveType.objects.filter(name=LeaveType.UNPAID).first()
        if unpaid_leave_type:
            unpaid_leave_count = Leave.objects.filter(leave_type=unpaid_leave_type).count()
        else:
            unpaid_leave_count = 0
    except (ImportError, Exception):
        unpaid_leave_count = 0

    system_log_count = SystemLog.objects.exclude(action=SystemLog.VIEW).count()

    # Get leave types
    try:
        from leaves.models import LeaveType
        leave_types = LeaveType.objects.all()
    except (ImportError, Exception):
        leave_types = []

    # Get recent employees
    recent_employees = Employee.objects.all().order_by('-created_at')[:5]

    # Get recent leaves
    try:
        from leaves.models import Leave
        recent_leaves = Leave.objects.all().order_by('-created_at')[:5]
    except (ImportError, Exception):
        recent_leaves = []

    # Get recent file movements
    try:
        recent_file_movements = FileMovement.objects.all().order_by('-created_at')[:5]
    except Exception as e:
        print(f"Error getting file movements: {e}")
        recent_file_movements = []

    # Get recent ranks
    recent_ranks = EmployeeRank.objects.all().order_by('-created_at')[:5]

    return render(request, 'dashboard.html', {
        'employee_count': employee_count,
        'department_count': department_count,
        'user_count': user_count,
        'active_leaves_count': active_leaves_count,
        'rank_count': rank_count,
        'file_count': file_count,
        'unpaid_leave_count': unpaid_leave_count,
        'system_log_count': system_log_count,
        'leave_types': leave_types,
        'recent_employees': recent_employees,
        'recent_leaves': recent_leaves,
        'recent_file_movements': recent_file_movements,
        'recent_ranks': recent_ranks,
    })

@login_required
def calculate_age(request):
    """View for calculating employee ages"""
    # Get all employees with birth_date
    employees = Employee.objects.filter(birth_date__isnull=False).order_by('full_name')

    # Calculate age for each employee
    today = date.today()
    for employee in employees:
        if employee.birth_date:
            born = employee.birth_date
            # Calculate years
            years = today.year - born.year - ((today.month, today.day) < (born.month, born.day))

            # Calculate months and days
            if today.day >= born.day:  # Same day or later in the month
                days = today.day - born.day
                if today.month >= born.month:  # Same or later month
                    months = today.month - born.month
                else:  # Earlier month, wrapped around from previous year
                    months = today.month + 12 - born.month
            else:  # Earlier day, wrapped around from previous month
                # Get days in the previous month
                if today.month == 1:  # January
                    prev_month = 12  # December of previous year
                    prev_month_year = today.year - 1
                else:
                    prev_month = today.month - 1
                    prev_month_year = today.year

                import calendar
                last_day_of_prev_month = calendar.monthrange(prev_month_year, prev_month)[1]
                days = today.day + last_day_of_prev_month - born.day

                if today.month > born.month:  # Later month in same year
                    months = today.month - 1 - born.month
                elif today.month == born.month:  # Same month
                    months = 11  # 11 months since last birthday
                else:  # Earlier month, wrapped from previous year
                    months = today.month + 11 - born.month

            # Store age components
            employee.age_years = years
            employee.age_months = months
            employee.age_days = days
        else:
            employee.age_years = None
            employee.age_months = None
            employee.age_days = None

    # Get current employment for each employee
    for employee in employees:
        employment = employee.employments.filter(is_current=True).first()
        if employment:
            employee.position_name = employment.position.name
        else:
            employee.position_name = '-'

    return render(request, 'employees/calculate_age.html', {
        'employees': employees,
        'today': today
    })

@login_required
def employee_list(request):
    search_query = request.GET.get('search', '')

    # Get filter parameters
    specialization_filter = request.GET.get('specialization', '')
    appointment_type_filter = request.GET.get('appointment_type', '')
    department_filter = request.GET.get('department', '')
    position_filter = request.GET.get('position', '')
    gender_filter = request.GET.get('gender', '')

    # Start with all employees
    employees = Employee.objects.all()

    # Apply search filter
    if search_query:
        employees = employees.filter(
            Q(ministry_number__icontains=search_query) |
            Q(national_id__icontains=search_query) |
            Q(full_name__icontains=search_query) |
            Q(school__icontains=search_query)
        )

    # Apply specialization filter
    if specialization_filter:
        employees = employees.filter(specialization__icontains=specialization_filter)

    # Apply gender filter
    if gender_filter:
        employees = employees.filter(gender=gender_filter)

    # Apply department filter (using school field)
    if department_filter:
        employees = employees.filter(school__icontains=department_filter)

    # Apply appointment type filter
    if appointment_type_filter:
        try:
            appointment_type_id = int(appointment_type_filter)
            employees = employees.filter(
                employments__appointment_type__id=appointment_type_id,
                employments__is_current=True
            )
        except (ValueError, TypeError):
            pass

    # Apply position filter
    if position_filter:
        try:
            position_id = int(position_filter)
            employees = employees.filter(
                employments__position__id=position_id,
                employments__is_current=True
            )
        except (ValueError, TypeError):
            pass

    # Remove duplicates that might occur due to joins
    employees = employees.distinct()

    # Get departments, positions and appointment types for the form and filters
    from employment.models import Department, Position, AppointmentType
    departments = Department.objects.all().order_by('name')
    positions = Position.objects.all().order_by('name')
    appointment_types = AppointmentType.objects.all().order_by('name')

    # Get unique specializations for filter dropdown
    specializations = Employee.objects.values_list('specialization', flat=True).distinct().order_by('specialization')
    specializations = [spec for spec in specializations if spec]  # Remove empty values

    # Get unique departments (schools) for filter dropdown
    unique_departments = Employee.objects.values_list('school', flat=True).distinct().order_by('school')
    unique_departments = [dept for dept in unique_departments if dept]  # Remove empty values

    # Create a form instance for the modal
    form = EmployeeForm()

    # Handle POST requests from the modal form
    if request.method == 'POST':
        form = EmployeeForm(request.POST)

        # Check if ministry number already exists
        ministry_number = request.POST.get('ministry_number')
        if ministry_number and Employee.objects.filter(ministry_number=ministry_number).exists():
            messages.error(request, 'الرقم الوزاري موجود بالفعل. الرجاء استخدام رقم وزاري آخر.')
            return render(request, 'employees/employee_data.html', {
                'employees': employees,
                'search_query': search_query,
                'departments': departments,
                'positions': positions,
                'appointment_types': appointment_types,
                'specializations': specializations,
                'unique_departments': unique_departments,
                'form': form,
                # Filter values to maintain state
                'specialization_filter': specialization_filter,
                'appointment_type_filter': appointment_type_filter,
                'department_filter': department_filter,
                'position_filter': position_filter,
                'gender_filter': gender_filter,
            })

        # Check if national ID already exists
        national_id = request.POST.get('national_id')
        if national_id and Employee.objects.filter(national_id=national_id).exists():
            messages.error(request, 'الرقم الوطني موجود بالفعل. الرجاء استخدام رقم وطني آخر.')
            return render(request, 'employees/employee_data.html', {
                'employees': employees,
                'search_query': search_query,
                'departments': departments,
                'positions': positions,
                'appointment_types': appointment_types,
                'specializations': specializations,
                'unique_departments': unique_departments,
                'form': form,
                # Filter values to maintain state
                'specialization_filter': specialization_filter,
                'appointment_type_filter': appointment_type_filter,
                'department_filter': department_filter,
                'position_filter': position_filter,
                'gender_filter': gender_filter,
            })

        if form.is_valid():
            employee = form.save()

            # Handle position if provided
            position_id = request.POST.get('position')
            if position_id:
                from employment.models import Employment, EmploymentStatus, EmployeePosition
                position = Position.objects.get(id=position_id)

                # Get or create a department based on the school field
                department, _ = Department.objects.get_or_create(name=employee.school)

                # Get or create a default employment status
                status, _ = EmploymentStatus.objects.get_or_create(
                    name='permanent',
                    defaults={'description': 'Permanent employment'}
                )

                # Get appointment type if provided
                appointment_type_id = request.POST.get('appointment_type')
                appointment_type = None
                if appointment_type_id:
                    appointment_type = AppointmentType.objects.get(id=appointment_type_id)

                # Create employment record
                Employment.objects.create(
                    employee=employee,
                    position=position,
                    department=department,
                    status=status,
                    appointment_type=appointment_type,
                    start_date=employee.hire_date,
                    is_current=True
                )

                # Also create an EmployeePosition record to track position history
                EmployeePosition.objects.create(
                    employee=employee,
                    position=position,
                    date_obtained=employee.hire_date,
                    notes="تم إنشاء المسمى الوظيفي عند إضافة الموظف"
                )

            messages.success(request, 'تم إضافة الموظف بنجاح.')

            # Create notification for new employee
            try:
                from notifications.utils import notify_employee_added
                notify_employee_added(employee, request.user)
            except ImportError:
                pass  # Notifications app not available

            return redirect('employees:employee_list')

    return render(request, 'employees/employee_data.html', {
        'employees': employees,
        'search_query': search_query,
        'departments': departments,
        'positions': positions,
        'appointment_types': appointment_types,
        'specializations': specializations,
        'unique_departments': unique_departments,
        'form': form,
        # Filter values to maintain state
        'specialization_filter': specialization_filter,
        'appointment_type_filter': appointment_type_filter,
        'department_filter': department_filter,
        'position_filter': position_filter,
        'gender_filter': gender_filter,
    })

@login_required
def employee_detail(request, pk):
    employee = get_object_or_404(Employee, pk=pk)

    # Get performance evaluations (annual reports)
    from performance.models import PerformanceEvaluation
    annual_reports = PerformanceEvaluation.objects.filter(employee=employee).order_by('-year')

    # Get penalties from disciplinary app
    penalties = employee.disciplinary_penalties.all()

    # Get current employment and appointment type
    from employment.models import Employment, AppointmentType
    current_employment = employee.employments.filter(is_current=True).first()
    appointment_types = AppointmentType.objects.all().order_by('name')

    # Initialize forms
    report_form = AnnualReportForm()

    # Handle annual report form submission
    if request.method == 'POST' and 'report_form' in request.POST:
        from performance.models import PerformanceEvaluation
        year = request.POST.get('year')
        score = request.POST.get('score')
        notes = request.POST.get('notes', '')

        if year and score:
            # Check if evaluation already exists for this employee and year
            existing_evaluation = PerformanceEvaluation.objects.filter(employee=employee, year=year).first()
            if existing_evaluation:
                # Update existing evaluation
                existing_evaluation.score = score
                existing_evaluation.comments = notes
                existing_evaluation.save()
                messages.success(request, 'تم تحديث التقرير السنوي بنجاح.')
            else:
                # Create new evaluation
                PerformanceEvaluation.objects.create(
                    employee=employee,
                    year=year,
                    score=score,
                    max_score=100.00,
                    comments=notes
                )
                messages.success(request, 'تم إضافة التقرير السنوي بنجاح.')
            return redirect('employees:employee_detail', pk=employee.pk)
        else:
            messages.error(request, 'الرجاء إدخال السنة والدرجة.')

    # Handle appointment type update
    if request.method == 'POST' and 'appointment_type_form' in request.POST:
        appointment_type_id = request.POST.get('appointment_type')
        if appointment_type_id:
            appointment_type = AppointmentType.objects.get(id=appointment_type_id)
            if current_employment:
                current_employment.appointment_type = appointment_type
                current_employment.save()
                messages.success(request, 'تم تحديث صفة التعيين بنجاح.')
            else:
                # Create new employment if none exists
                from employment.models import EmploymentStatus, Department
                department, _ = Department.objects.get_or_create(name=employee.school)
                status, _ = EmploymentStatus.objects.get_or_create(
                    name='permanent',
                    defaults={'description': 'Permanent employment'}
                )
                Employment.objects.create(
                    employee=employee,
                    department=department,
                    status=status,
                    appointment_type=appointment_type,
                    start_date=employee.hire_date,
                    is_current=True
                )
                messages.success(request, 'تم إضافة صفة التعيين بنجاح.')
            return redirect('employees:employee_detail', pk=employee.pk)

    # Get current year for the report form
    from datetime import date
    current_year = date.today().year

    return render(request, 'employees/employee_detail.html', {
        'employee': employee,
        'annual_reports': annual_reports,
        'penalties': penalties,
        'report_form': report_form,
        'current_employment': current_employment,
        'appointment_types': appointment_types,
        'current_year': current_year
    })

# This view is no longer needed as it's integrated into employee_list
# @login_required
# def employee_create(request):
#     if request.method == 'POST':
#         form = EmployeeForm(request.POST)
#         if form.is_valid():
#             employee = form.save()
#             messages.success(request, 'تم إضافة الموظف بنجاح.')
#             return redirect('employees:employee_detail', pk=employee.pk)
#     else:
#         form = EmployeeForm()
#     return render(request, 'employees/employee_form.html', {'form': form})

@login_required
def annual_report_create(request):
    """View for creating a new annual report"""
    # Import PerformanceEvaluation model
    from performance.models import PerformanceEvaluation

    if request.method == 'POST':
        print('\n\nPOST data:', request.POST)
        form = AnnualReportForm(request.POST)
        print('Form is bound:', form.is_bound)
        if form.is_valid():
            try:
                print('Form is valid. Cleaned data:', form.cleaned_data)

                # Get employee from employee_id
                employee_id = form.cleaned_data.get('employee_id')
                ministry_number = form.cleaned_data.get('ministry_number')

                # Find employee
                employee = None
                if employee_id:
                    try:
                        employee = Employee.objects.get(id=employee_id)
                        print(f'Found employee by ID: {employee.id} - {employee.full_name}')
                    except Employee.DoesNotExist:
                        messages.error(request, 'لم يتم العثور على الموظف.')
                        return render(request, 'employees/annual_report_form.html', {'form': form})
                elif ministry_number:
                    try:
                        employee = Employee.objects.get(ministry_number=ministry_number)
                        print(f'Found employee by ministry number: {employee.id} - {employee.full_name}')
                    except Employee.DoesNotExist:
                        messages.error(request, 'لم يتم العثور على موظف بهذا الرقم الوزاري')
                        return render(request, 'employees/annual_report_form.html', {'form': form})
                else:
                    messages.error(request, 'الرجاء إدخال الرقم الوزاري للموظف')
                    return render(request, 'employees/annual_report_form.html', {'form': form})

                # Get form data
                year = form.cleaned_data['year']
                score = form.cleaned_data['score']
                notes = form.cleaned_data.get('notes', '')

                print(f'Creating performance evaluation for {employee.full_name}, year: {year}, score: {score}')

                # Check if evaluation already exists for this employee and year
                existing_evaluation = PerformanceEvaluation.objects.filter(employee=employee, year=year).first()
                if existing_evaluation:
                    # Update existing evaluation
                    existing_evaluation.score = score
                    existing_evaluation.comments = notes
                    existing_evaluation.save()
                    print(f'Updated existing evaluation with ID: {existing_evaluation.id}')
                    evaluation = existing_evaluation
                else:
                    # Create new PerformanceEvaluation object
                    evaluation = PerformanceEvaluation(
                        employee=employee,
                        year=year,
                        score=score,
                        max_score=100.00,  # Default max score
                        comments=notes
                    )

                    # Save the evaluation
                    evaluation.save()
                    print('Created new performance evaluation with ID:', evaluation.id)

                messages.success(request, 'تم إضافة التقرير السنوي بنجاح.')
                return redirect('performance:performance_list')

            except Exception as e:
                print('Error saving annual report:', str(e))
                messages.error(request, f'حدث خطأ أثناء حفظ التقرير السنوي: {str(e)}')
        else:
            print('\nForm is invalid. Errors:', form.errors)
    else:
        # Initialize form with default values
        from datetime import date
        form = AnnualReportForm(initial={'year': date.today().year})

    return render(request, 'employees/annual_report_form.html', {'form': form})

@login_required
def get_employee_by_ministry_number(request):
    """AJAX view to get employee details by ministry number"""
    ministry_number = request.GET.get('ministry_number', '')
    if not ministry_number:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوزاري'})

    try:
        employee = Employee.objects.get(ministry_number=ministry_number)
        return JsonResponse({
            'success': True,
            'employee': {
                'id': employee.id,
                'full_name': employee.full_name,
                'ministry_number': employee.ministry_number
            }
        })
    except Employee.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري'})

@login_required
def employee_update(request, pk):
    employee = get_object_or_404(Employee, pk=pk)

    # Get departments, positions and appointment types for the form
    from employment.models import Department, Position, AppointmentType
    departments = Department.objects.all().order_by('name')
    positions = Position.objects.all().order_by('name')
    appointment_types = AppointmentType.objects.all().order_by('name')

    if request.method == 'POST':
        form = EmployeeForm(request.POST, instance=employee)

        # Check if ministry number already exists (but not for this employee)
        ministry_number = request.POST.get('ministry_number')
        if ministry_number and Employee.objects.filter(ministry_number=ministry_number).exclude(id=employee.id).exists():
            messages.error(request, 'الرقم الوزاري موجود بالفعل لموظف آخر. الرجاء استخدام رقم وزاري آخر.')
            return render(request, 'employees/employee_form.html', {
                'form': form,
                'employee': employee,
                'departments': departments,
                'positions': positions
            })

        # Check if national ID already exists (but not for this employee)
        national_id = request.POST.get('national_id')
        if national_id and Employee.objects.filter(national_id=national_id).exclude(id=employee.id).exists():
            messages.error(request, 'الرقم الوطني موجود بالفعل لموظف آخر. الرجاء استخدام رقم وطني آخر.')
            return render(request, 'employees/employee_form.html', {
                'form': form,
                'employee': employee,
                'departments': departments,
                'positions': positions
            })

        if form.is_valid():
            form.save()

            # Handle position if provided
            position_id = request.POST.get('position')
            if position_id:
                from employment.models import Employment, EmploymentStatus, EmployeePosition
                position = Position.objects.get(id=position_id)

                # Get or create a department based on the school field
                department, _ = Department.objects.get_or_create(name=employee.school)

                # Get or create a default employment status
                status, _ = EmploymentStatus.objects.get_or_create(
                    name='permanent',
                    defaults={'description': 'Permanent employment'}
                )

                # Get appointment type if provided
                appointment_type_id = request.POST.get('appointment_type')
                appointment_type = None
                if appointment_type_id:
                    appointment_type = AppointmentType.objects.get(id=appointment_type_id)

                # Check if employee already has an employment
                current_employment = employee.employments.filter().first()
                if current_employment:
                    # Check if position has changed
                    position_changed = current_employment.position != position

                    # Update existing employment
                    current_employment.position = position
                    current_employment.department = department
                    current_employment.appointment_type = appointment_type
                    current_employment.save()

                    # If position has changed, create a new EmployeePosition record
                    if position_changed:
                        EmployeePosition.objects.create(
                            employee=employee,
                            position=position,
                            date_obtained=timezone.now().date(),
                            notes="تم تحديث المسمى الوظيفي"
                        )
                else:
                    # Create new employment
                    Employment.objects.create(
                        employee=employee,
                        position=position,
                        department=department,
                        status=status,
                        appointment_type=appointment_type,
                        start_date=employee.hire_date,
                        is_current=True
                    )

                    # Create an EmployeePosition record
                    EmployeePosition.objects.create(
                        employee=employee,
                        position=position,
                        date_obtained=employee.hire_date,
                        notes="تم إنشاء المسمى الوظيفي عند تحديث الموظف"
                    )

            messages.success(request, 'تم تحديث بيانات الموظف بنجاح.')
            return redirect('employees:employee_list')
    else:
        form = EmployeeForm(instance=employee)

    # Get current position for the form
    current_position = None
    current_employment = employee.employments.filter().first()
    if current_employment:
        current_position = current_employment.position

    return render(request, 'employees/employee_form.html', {
        'form': form,
        'employee': employee,
        'departments': departments,
        'positions': positions,
        'appointment_types': appointment_types,
        'current_position': current_position
    })

@login_required
def employee_delete(request, pk):
    employee = get_object_or_404(Employee, pk=pk)
    if request.method == 'POST':
        employee_name = employee.full_name  # Store name before deletion
        employee.delete()
        messages.success(request, 'تم حذف الموظف بنجاح.')

        # Create notification for employee deletion
        try:
            from notifications.utils import notify_admins
            notify_admins(
                "تم حذف موظف",
                f"تم حذف الموظف {employee_name} من النظام",
                "warning",
                "fa-user-minus"
            )
        except ImportError:
            pass  # Notifications app not available

        return redirect('employees:employee_list')

    # Get all employees for the list
    search_query = request.GET.get('search', '')
    if search_query:
        employees = Employee.objects.filter(
            Q(ministry_number__icontains=search_query) |
            Q(national_id__icontains=search_query) |
            Q(full_name__icontains=search_query) |
            Q(school__icontains=search_query)
        )
    else:
        employees = Employee.objects.all()

    # Get departments and positions for the form
    from employment.models import Department, Position
    departments = Department.objects.all().order_by('name')
    positions = Position.objects.all().order_by('name')

    return render(request, 'employees/employee_data.html', {
        'employee_to_delete': employee,
        'employees': employees,
        'search_query': search_query,
        'is_delete': True,
        'departments': departments,
        'positions': positions
    })

@login_required
def employee_import_export(request):
    if request.method == 'POST' and request.FILES.get('excel_file'):
        form = EmployeeImportForm(request.POST, request.FILES)
        if form.is_valid():
            excel_file = request.FILES['excel_file']
            try:
                # Read the Excel file
                df = pd.read_excel(excel_file)

                # Keep track of imported and skipped employees
                imported_count = 0
                skipped_count = 0
                skipped_employees = []

                # Process each row
                for _, row in df.iterrows():
                    try:
                        # Check if employee already exists
                        ministry_number = str(row.get('الرقم الوزاري', ''))
                        if not ministry_number:
                            skipped_count += 1
                            skipped_employees.append(f"صف {_ + 2}: الرقم الوزاري غير موجود")
                            continue

                        if Employee.objects.filter(ministry_number=ministry_number).exists():
                            skipped_count += 1
                            skipped_employees.append(f"الرقم الوزاري {ministry_number}: موجود بالفعل")
                            continue

                        # Check if national ID already exists
                        national_id = str(row.get('الرقم الوطني', ''))
                        if national_id and Employee.objects.filter(national_id=national_id).exists():
                            skipped_count += 1
                            skipped_employees.append(f"الرقم الوطني {national_id}: موجود بالفعل")
                            continue

                        # Get gender value
                        gender_value = str(row.get('الجنس', ''))
                        gender = 'male' if gender_value == 'ذكر' else 'female' if gender_value == 'انثى' else None

                        # Create new employee
                        Employee.objects.create(
                            ministry_number=ministry_number,
                            national_id=national_id,
                            full_name=str(row.get('الاسم الكامل', '')),
                            qualification=str(row.get('المؤهل العلمي', '')),
                            specialization=str(row.get('التخصص', '')),
                            hire_date=row.get('تاريخ التعيين'),
                            school=str(row.get('القسم', '') or row.get('المدرسة', '')),
                            birth_date=row.get('تاريخ الميلاد'),
                            address=str(row.get('العنوان', '')),
                            phone_number=str(row.get('رقم الهاتف', '')),
                            gender=gender
                        )
                        imported_count += 1
                    except Exception as e:
                        # Log the error and continue with the next row
                        print(f"Error importing row: {e}")
                        continue

                # Show success message with details
                if imported_count > 0:
                    messages.success(request, f'تم استيراد {imported_count} موظف بنجاح.')

                # Show warning for skipped employees
                if skipped_count > 0:
                    skipped_message = f'تم تخطي {skipped_count} موظف لأن الرقم الوزاري أو الرقم الوطني موجود بالفعل.'
                    if len(skipped_employees) > 0:
                        skipped_details = '\n'.join(skipped_employees[:10])
                        if len(skipped_employees) > 10:
                            skipped_details += f'\n... و{len(skipped_employees) - 10} أخرى'
                        skipped_message += f'\n{skipped_details}'
                    messages.warning(request, skipped_message)
                return redirect('employees:employee_list')
            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء استيراد البيانات: {str(e)}')
        else:
            messages.error(request, 'الرجاء تصحيح الأخطاء أدناه.')
    else:
        form = EmployeeImportForm()

    # Get employee statistics
    employees = Employee.objects.all()
    employees_count = employees.count()
    male_count = employees.filter(gender='male').count()
    female_count = employees.filter(gender='female').count()
    departments_count = len(set([e.school for e in employees if e.school]))

    context = {
        'form': form,
        'employees_count': employees_count,
        'male_count': male_count,
        'female_count': female_count,
        'departments_count': departments_count,
    }

    if request.GET.get('export') == 'excel':
        # Export to Excel
        employees = Employee.objects.all()
        data = {
            'الرقم الوزاري': [e.ministry_number for e in employees],
            'الرقم الوطني': [e.national_id for e in employees],
            'الاسم الكامل': [e.full_name for e in employees],
            'المؤهل العلمي': [e.qualification for e in employees],
            'التخصص': [e.specialization for e in employees],
            'تاريخ التعيين': [e.hire_date for e in employees],
            'القسم': [e.school for e in employees],
            'تاريخ الميلاد': [e.birth_date for e in employees],
            'العنوان': [e.address for e in employees],
            'رقم الهاتف': [e.phone_number for e in employees],
            'الجنس': ['ذكر' if e.gender == 'male' else 'انثى' if e.gender == 'female' else '' for e in employees],
        }
        df = pd.DataFrame(data)

        # Create a response with Excel file
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='الموظفين')

            # Get the worksheet
            workbook = writer.book
            worksheet = writer.sheets['الموظفين']

            # Format the worksheet for Arabic
            worksheet.sheet_view.rightToLeft = True

            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2) * 1.2
                worksheet.column_dimensions[column_letter].width = adjusted_width

            # Add formatting
            header_font = Font(bold=True, size=12)
            header_fill = PatternFill(start_color='E0E0E0', end_color='E0E0E0', fill_type='solid')
            border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # Apply formatting to header row
            for cell in worksheet[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.border = border

            # Apply borders to all cells
            for row in worksheet.iter_rows(min_row=2):
                for cell in row:
                    cell.border = border

        output.seek(0)
        response = HttpResponse(output.read(), content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename=employees.xlsx'
        return response

    # PDF export option
    if request.GET.get('export') == 'pdf':
        return export_employees_pdf(request)

    return render(request, 'employees/employee_import_export.html', context)


@login_required
def export_employees_pdf(request):
    """View for exporting employee data as PDF with proper Arabic support using arabic_reshaper and bidi"""
    # Get all employees
    employees = Employee.objects.all().order_by('full_name')

    # Create a response object with PDF content type
    response = HttpResponse(content_type='application/pdf; charset=utf-8')
    response['Content-Disposition'] = 'attachment; filename="employees_data.pdf"'

    # Register Arabic fonts - we'll register multiple fonts for better compatibility
    fonts_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static', 'fonts')

    # Register Noto Sans Arabic font
    noto_sans_path = os.path.join(fonts_dir, 'NotoSansArabic-Regular.ttf')
    pdfmetrics.registerFont(TTFont('NotoSansArabic', noto_sans_path))

    # Register Arial font as fallback
    arial_path = os.path.join(fonts_dir, 'arial.ttf')
    if os.path.exists(arial_path):
        pdfmetrics.registerFont(TTFont('Arial', arial_path))

    # Create the PDF document with RTL support and landscape orientation for wider table
    doc = SimpleDocTemplate(
        response,
        pagesize=landscape(A4),  # Use landscape orientation for wider table
        rightMargin=10,  # Further reduced margins to maximize content space
        leftMargin=10,
        topMargin=10,
        bottomMargin=30,  # Further reduced bottom margin but still enough for the date
        encoding='UTF-8'  # Explicitly set UTF-8 encoding
    )

    # Create styles
    styles = getSampleStyleSheet()

    # Create a custom style for Arabic text with improved settings
    arabic_style = ParagraphStyle(
        'ArabicStyle',
        parent=styles['Normal'],
        fontName='NotoSansArabic',
        fontSize=8,  # Increased font size by one degree
        alignment=1,  # Center alignment
        leading=9,  # Increased line height
        spaceAfter=0,  # No space after
        firstLineIndent=0,
        rightIndent=0,
        leftIndent=0,
        wordWrap='LTR',  # Force left-to-right to prevent wrapping
        splitLongWords=0  # Prevent splitting long words
    )

    arabic_header_style = ParagraphStyle(
        'ArabicHeaderStyle',
        parent=styles['Heading1'],
        fontName='NotoSansArabic',
        fontSize=18,
        alignment=1,  # Center alignment
        leading=24,
        spaceAfter=12,
        textColor=colors.darkblue
    )

    # Define light beige color for table headers (light sandy color)
    light_beige = colors.Color(0.94, 0.90, 0.80)

    # Helper function to process Arabic text
    def process_arabic_text(text, max_length=None):
        if not text:
            return ""
        # Convert to string
        text_str = str(text)

        # Special handling for dates to ensure they display correctly
        if '-' in text_str and len(text_str) == 10:  # This is likely a date in format dd-mm-yyyy
            # For dates, we want to preserve the format exactly as is
            parts = text_str.split('-')
            if len(parts) == 3:
                # Reshape each part separately to prevent reshaping issues with numbers
                day = arabic_reshaper.reshape(parts[0])
                month = arabic_reshaper.reshape(parts[1])
                year = arabic_reshaper.reshape(parts[2])
                # Combine with explicit RTL markers to ensure correct display
                return get_display(day + '-' + month + '-' + year)

        # Truncate if max_length is specified and text is longer
        if max_length and len(text_str) > max_length:
            text_str = text_str[:max_length]

        # Reshape Arabic text
        reshaped_text = arabic_reshaper.reshape(text_str)
        # Apply bidirectional algorithm
        bidi_text = get_display(reshaped_text)
        return bidi_text

    # Create the content elements
    elements = []

    # Add title
    title_text = process_arabic_text('بيانات الموظفين')
    title = Paragraph(title_text, arabic_header_style)
    elements.append(title)
    elements.append(Spacer(1, 20))

    # Get the data from the table in the UI
    # Prepare table headers with processed Arabic text - in RTL order
    headers = [
        process_arabic_text('العنوان'),
        process_arabic_text('تاريخ الميلاد'),
        process_arabic_text('الجنس'),
        process_arabic_text('رقم الهاتف'),
        process_arabic_text('تاريخ التعيين'),
        process_arabic_text('التخصص'),
        process_arabic_text('المؤهل العلمي'),
        process_arabic_text('الرقم الوطني'),
        process_arabic_text('اسم الموظف'),
        process_arabic_text('الرقم الوزاري'),
    ]

    # Create table data with processed headers
    data = [[Paragraph(header, arabic_style) for header in headers]]

    # Add data rows with processed Arabic text
    for employee in employees:
        # Format birth date with clear separators
        birth_date = ""
        if employee.birth_date:
            birth_date = employee.birth_date.strftime("%d-%m-%Y")  # Using dashes for better readability

        # Format hire date with clear separators
        hire_date = ""
        if employee.hire_date:
            hire_date = employee.hire_date.strftime("%d-%m-%Y")  # Using dashes for better readability

        # Format gender
        gender = ""
        if employee.gender == 'male':
            gender = "ذكر"
        elif employee.gender == 'female':
            gender = "أنثى"

        # Process each text field - don't limit qualification to show full content
        ministry_number = process_arabic_text(employee.ministry_number, max_length=15)
        full_name = process_arabic_text(employee.full_name, max_length=30)
        national_id = process_arabic_text(employee.national_id or "", max_length=15)
        qualification = process_arabic_text(employee.qualification or "")  # No limit to show full content
        specialization = process_arabic_text(employee.specialization or "", max_length=18)  # Reduced specialization length
        hire_date_processed = process_arabic_text(hire_date, max_length=10)  # Ensure date is fixed length
        phone_number = process_arabic_text(employee.phone_number or "", max_length=15)  # Ensure phone number is fixed length
        gender_processed = process_arabic_text(gender, max_length=5)
        birth_date_processed = process_arabic_text(birth_date, max_length=10)  # Ensure date is fixed length
        address = process_arabic_text(employee.address or "", max_length=25)  # Reduced address length

        # Create special style for date fields to ensure they don't wrap
        date_style = ParagraphStyle(
            'DateStyle',
            parent=arabic_style,
            fontName='NotoSansArabic',
            fontSize=8,  # Increased font size
            alignment=1,
            wordWrap=None,
            splitLongWords=0
        )

        # Create special style for qualification field - allow wrapping to show full content
        qualification_style = ParagraphStyle(
            'QualificationStyle',
            parent=arabic_style,
            fontName='NotoSansArabic',
            fontSize=8,  # Keep font size
            alignment=1,
            leading=10,  # Increased line height for wrapped text
            wordWrap='CJK',  # Allow wrapping to show full content
            splitLongWords=1  # Allow splitting long words if needed
        )

        # Add row in RTL order - using special styles for different fields
        row = [
            Paragraph(address, arabic_style),
            Paragraph(birth_date_processed, date_style),  # Use date style
            Paragraph(gender_processed, arabic_style),
            Paragraph(phone_number, arabic_style),
            Paragraph(hire_date_processed, date_style),  # Use date style
            Paragraph(specialization, arabic_style),
            Paragraph(qualification, qualification_style),  # Use qualification style to allow wrapping
            Paragraph(national_id, arabic_style),
            Paragraph(full_name, arabic_style),
            Paragraph(ministry_number, arabic_style),
        ]
        data.append(row)

    # Create the table with optimized column widths for single-line display in landscape mode
    # Wider columns for text that might be longer, taking advantage of landscape orientation
    # Adjusted column widths to better fit the content - further reducing address and specialization, further increasing qualification
    col_widths = [90, 60, 35, 60, 60, 90, 160, 65, 130, 55]  # Further reduced address and specialization, further increased qualification
    table = Table(data, repeatRows=1, colWidths=col_widths)

    # Style the table with improved settings for Arabic
    table_style = TableStyle([
        # Header row styling - changed to light beige color
        ('BACKGROUND', (0, 0), (-1, 0), light_beige),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),  # Changed to black for better readability on light background
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('FONTNAME', (0, 0), (-1, 0), 'NotoSansArabic'),
        ('FONTSIZE', (0, 0), (-1, 0), 9),  # Keep header font size
        ('BOTTOMPADDING', (0, 0), (-1, 0), 3),  # Keep padding
        ('TOPPADDING', (0, 0), (-1, 0), 3),  # Keep padding

        # Data rows styling
        ('BACKGROUND', (0, 1), (-1, -1), colors.white),
        ('FONTNAME', (0, 1), (-1, -1), 'NotoSansArabic'),
        ('FONTSIZE', (0, 1), (-1, -1), 8),  # Keep font size for data rows
        ('BOTTOMPADDING', (0, 1), (-1, -1), 4),  # Increased padding for wrapped text
        ('TOPPADDING', (0, 1), (-1, -1), 4),  # Increased padding for wrapped text

        # Grid styling
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('BOX', (0, 0), (-1, -1), 2, colors.black),
        ('LINEBELOW', (0, 0), (-1, 0), 2, colors.black),

        # Padding - reduced to maximize space
        ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ('LEFTPADDING', (0, 0), (-1, -1), 8),
    ])

    # Add zebra striping for better readability
    for i in range(1, len(data)):
        if i % 2 == 0:
            table_style.add('BACKGROUND', (0, i), (-1, i), colors.lightgrey)

    table.setStyle(table_style)

    # Add the table to the elements
    elements.append(table)

    # Add footer with page numbers and date
    def add_page_number_and_date(canvas, doc):
        canvas.saveState()
        canvas.setFont('NotoSansArabic', 10)

        # Add page number
        page_num = canvas.getPageNumber()
        page_text = process_arabic_text(f"صفحة {page_num}")
        canvas.drawRightString(540, 30, page_text)

        # Add current date at the bottom of the page
        today = date.today()
        formatted_date = f"{today.day}/{today.month}/{today.year}"
        date_text = process_arabic_text(f"تاريخ التقرير: {formatted_date}")
        canvas.drawCentredString(doc.pagesize[0]/2, 30, date_text)

        canvas.restoreState()

    # Build the PDF document with page numbers and date
    doc.build(elements, onFirstPage=add_page_number_and_date, onLaterPages=add_page_number_and_date)

    return response


@login_required
def get_employee_by_ministry_number(request):
    """AJAX view to get employee details by ministry number"""
    ministry_number = request.GET.get('ministry_number', '')
    if not ministry_number:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوزاري'})

    try:
        employee = Employee.objects.get(ministry_number=ministry_number)
        return JsonResponse({
            'success': True,
            'employee': {
                'id': employee.id,
                'full_name': employee.full_name,
                'ministry_number': employee.ministry_number
            }
        })
    except Employee.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري'})
