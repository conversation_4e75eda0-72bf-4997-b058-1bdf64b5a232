from django import forms
from django.utils.crypto import get_random_string
from .models import InternalTransfer, ApprovedForm, TechnicalTransfer
from employment.models import Department
from employees.models import Employee
from employment.models import EmployeeIdentification

class InternalTransferForm(forms.ModelForm):
    """Form for internal transfer requests"""

    # Campo para buscar por número ministerial
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'أدخل الرقم الوزاري للبحث',
            'id': 'ministry_number_input'
        })
    )

    def clean(self):
        cleaned_data = super().clean()
        ministry_number = cleaned_data.get('ministry_number')
        employee_id = cleaned_data.get('employee_id')

        # Verificar que el número ministerial existe
        try:
            employee = Employee.objects.get(ministry_number=ministry_number)
        except Employee.DoesNotExist:
            raise forms.ValidationError('لم يتم العثور على موظف بهذا الرقم الوزاري')

        # Verificar que el número nacional coincide con el empleado
        if employee.national_id and employee_id and employee.national_id != employee_id:
            raise forms.ValidationError(f'الرقم الوطني المدخل ({employee_id}) لا يتطابق مع الرقم الوطني للموظف ({employee.national_id})')

        # Verificar si el empleado tiene un número de identificación
        try:
            identification = EmployeeIdentification.objects.get(employee=employee)
            # Si existe una identificación, verificar que el número nacional coincide
            if identification.national_id and employee_id and identification.national_id != employee_id:
                raise forms.ValidationError(f'الرقم الوطني المدخل ({employee_id}) لا يتطابق مع الرقم الوطني في بيانات التعريف ({identification.national_id})')

            # Verificar que el número de identificación existe
            if not identification.id_number:
                raise forms.ValidationError(f'الموظف {employee.full_name} ليس لديه رقم هوية مسجل في النظام. يرجى إضافة رقم الهوية أولاً.')
        except EmployeeIdentification.DoesNotExist:
            # Si no existe una identificación, mostrar un mensaje
            raise forms.ValidationError(f'الموظف {employee.full_name} ليس لديه بيانات تعريفية مسجلة في النظام. يرجى إضافة البيانات التعريفية أولاً.')

        return cleaned_data

    class Meta:
        model = InternalTransfer
        fields = [
            'ministry_number', 'employee_name', 'employee_id', 'gender',
            'current_department', 'hire_date', 'last_position',
            'qualification', 'specialization', 'last_rank', 'address',
            'first_choice', 'second_choice', 'third_choice',
            'reason', 'phone_number', 'email'
        ]
        widgets = {
            'employee_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'سيتم تعبئته تلقائياً بعد البحث',
                'readonly': 'readonly'
            }),
            'employee_id': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'سيتم تعبئته تلقائياً بعد البحث',
                'readonly': 'readonly'
            }),
            'gender': forms.Select(attrs={
                'class': 'form-control'
            }),
            'current_department': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'سيتم تعبئته تلقائياً بعد البحث',
                'readonly': 'readonly'
            }),
            'hire_date': forms.DateInput(attrs={
                'class': 'form-control',
                'readonly': 'readonly'
            }),
            'actual_service': forms.TextInput(attrs={
                'class': 'form-control',
                'readonly': 'readonly'
            }),
            'last_position': forms.TextInput(attrs={
                'class': 'form-control',
                'readonly': 'readonly'
            }),
            'qualification': forms.TextInput(attrs={
                'class': 'form-control',
                'readonly': 'readonly'
            }),
            'specialization': forms.TextInput(attrs={
                'class': 'form-control',
                'readonly': 'readonly'
            }),
            'last_rank': forms.TextInput(attrs={
                'class': 'form-control',
                'readonly': 'readonly'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'readonly': 'readonly'
            }),
            'first_choice': forms.Select(attrs={
                'class': 'form-control'
            }),
            'second_choice': forms.Select(attrs={
                'class': 'form-control'
            }),
            'third_choice': forms.Select(attrs={
                'class': 'form-control'
            }),
            'reason': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'سبب طلب النقل'
            }),
            'phone_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الهاتف للتواصل'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'البريد الإلكتروني (اختياري)'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Get school departments for choices
        school_departments = Department.objects.filter(workplace='school').order_by('name')

        # Add choices for department fields
        department_choices = [(dept.name, dept.name) for dept in school_departments]
        department_choices.insert(0, ('', '-- اختر القسم --'))

        self.fields['first_choice'].widget.choices = department_choices
        self.fields['second_choice'].widget.choices = department_choices
        self.fields['third_choice'].widget.choices = department_choices

        # Generate edit token if this is a new instance
        if not self.instance.pk and not self.instance.edit_token:
            self.instance.edit_token = get_random_string(length=20)

    def save(self, commit=True):
        instance = super().save(commit=False)

        # Generate edit token if not already set
        if not instance.edit_token:
            instance.edit_token = get_random_string(length=20)

        if commit:
            instance.save()

        return instance


class ApprovedFormForm(forms.ModelForm):
    """Form for adding and editing approved forms"""

    class Meta:
        model = ApprovedForm
        fields = ['title', 'description', 'file', 'is_active', 'order']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'عنوان النموذج'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'وصف النموذج (اختياري)'
            }),
            'file': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.zip,.rar'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'order': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 0
            }),
        }


class TechnicalTransferForm(forms.ModelForm):
    """Form for technical transfers"""

    # Field for searching by ministry number
    ministry_number = forms.CharField(
        label='الرقم الوزاري',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'أدخل الرقم الوزاري للبحث',
            'id': 'ministry_number_input'
        })
    )

    class Meta:
        model = TechnicalTransfer
        fields = ['ministry_number', 'employee_name', 'current_department', 'new_department', 'notes']
        widgets = {
            'employee_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'سيتم تعبئته تلقائياً بعد البحث',
                'readonly': 'readonly'
            }),
            'current_department': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'سيتم تعبئته تلقائياً بعد البحث',
                'readonly': 'readonly'
            }),
            'new_department': forms.Select(attrs={
                'class': 'form-control',
                'placeholder': 'اختر مركز العمل الجديد'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات (اختياري)'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Get school departments for choices
        school_departments = Department.objects.filter(workplace='school').order_by('name')

        # Add choices for department fields
        department_choices = [(dept.name, dept.name) for dept in school_departments]
        department_choices.insert(0, ('', '-- اختر مركز العمل الجديد --'))

        self.fields['new_department'].widget.choices = department_choices
