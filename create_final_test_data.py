#!/usr/bin/env python
"""
Create final test data for complete testing
"""

import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hr_system.settings')
django.setup()

from employees.models import Employee
from employment.models import BtecField, BtecTeacher, EmployeeIdentification
from datetime import date

def create_final_test_data():
    """Create final test data"""
    
    print("🎯 Creating Final Test Data")
    print("=" * 50)
    
    # Clean up existing test data
    Employee.objects.filter(ministry_number__in=["TEST001", "BTEC001"]).delete()
    BtecField.objects.filter(name="تقنية المعلومات النهائي").delete()
    
    # Create regular employee
    regular_employee = Employee.objects.create(
        ministry_number="TEST001",
        full_name="أحمد محمد علي",
        national_id="TEST001123",
        gender="male",
        qualification="بكالوريوس",
        specialization="رياضيات",
        school="مدرسة الاختبار الثانوية",
        hire_date=date(2020, 1, 1),
        birth_date=date(1990, 1, 1),
        address="عمان - الأردن",
        phone_number="0791234567"
    )
    
    regular_identification = EmployeeIdentification.objects.create(
        employee=regular_employee,
        ministry_number="TEST001",
        national_id="TEST001123",
        id_number="TEST001456",
        birth_day=1,
        birth_month=1,
        birth_year=1990,
        address="عمان - الأردن"
    )
    
    # Create BTEC teacher
    btec_employee = Employee.objects.create(
        ministry_number="BTEC001",
        full_name="فاطمة أحمد محمد",
        national_id="BTEC001123",
        gender="female",
        qualification="ماجستير",
        specialization="تقنية معلومات",
        school="مدرسة الاختبار الثانوية",
        hire_date=date(2021, 1, 1),
        birth_date=date(1985, 5, 15),
        address="إربد - الأردن",
        phone_number="0790987654"
    )
    
    btec_identification = EmployeeIdentification.objects.create(
        employee=btec_employee,
        ministry_number="BTEC001",
        national_id="BTEC001123",
        id_number="BTEC001456",
        birth_day=15,
        birth_month=5,
        birth_year=1985,
        address="إربد - الأردن"
    )
    
    btec_field = BtecField.objects.create(
        name="تقنية المعلومات النهائي",
        description="حقل تقنية المعلومات النهائي للاختبار"
    )
    
    btec_teacher = BtecTeacher.objects.create(
        employee=btec_employee,
        field=btec_field
    )
    
    print("✅ Final test data created successfully!")
    print("\n" + "=" * 50)
    print("🧪 COMPLETE TESTING GUIDE")
    print("=" * 50)
    
    print("\n🌐 افتح الصفحة: http://127.0.0.1:8000/internal-transfer/")
    print("🔧 افتح أدوات المطور (F12) وانتقل إلى Console")
    
    print("\n" + "=" * 30)
    print("1️⃣ اختبار الموظف العادي")
    print("=" * 30)
    print("📝 أدخل البيانات التالية:")
    print(f"   الرقم الوزاري: {regular_employee.ministry_number}")
    print(f"   الرقم الوطني: {regular_employee.national_id}")
    print(f"   رقم الهوية: {regular_identification.id_number}")
    print("🔍 اضغط على زر 'بحث والتحقق من البيانات'")
    print("✅ النتيجة المتوقعة:")
    print("   - يظهر نموذج تقديم الطلب")
    print("   - تمتلئ جميع الحقول بالبيانات")
    print("   - لا تظهر أي رسائل خطأ")
    
    print("\n" + "=" * 30)
    print("2️⃣ اختبار معلم BTEC")
    print("=" * 30)
    print("📝 أدخل البيانات التالية:")
    print(f"   الرقم الوزاري: {btec_employee.ministry_number}")
    print(f"   الرقم الوطني: {btec_employee.national_id}")
    print(f"   رقم الهوية: {btec_identification.id_number}")
    print("🔍 اضغط على زر 'بحث والتحقق من البيانات'")
    print("❌ النتيجة المتوقعة:")
    print("   - تظهر رسالة خطأ حمراء أسفل زر البحث")
    print("   - الرسالة تذكر BTEC وعدم السماح بالنقل")
    print("   - لا يظهر نموذج تقديم الطلب")
    
    print("\n" + "=" * 30)
    print("3️⃣ اختبار بيانات خاطئة")
    print("=" * 30)
    print("📝 أدخل البيانات التالية:")
    print("   الرقم الوزاري: WRONG123")
    print("   الرقم الوطني: WRONG456")
    print("   رقم الهوية: WRONG789")
    print("🔍 اضغط على زر 'بحث والتحقق من البيانات'")
    print("❌ النتيجة المتوقعة:")
    print("   - تظهر رسالة خطأ حمراء")
    print("   - الرسالة تقول 'لم يتم العثور على موظف'")
    print("   - لا يظهر نموذج تقديم الطلب")
    
    print("\n" + "=" * 30)
    print("4️⃣ اختبار بيانات متضاربة")
    print("=" * 30)
    print("📝 أدخل البيانات التالية:")
    print(f"   الرقم الوزاري: {regular_employee.ministry_number}")
    print("   الرقم الوطني: WRONG123")
    print(f"   رقم الهوية: {regular_identification.id_number}")
    print("🔍 اضغط على زر 'بحث والتحقق من البيانات'")
    print("❌ النتيجة المتوقعة:")
    print("   - تظهر رسالة خطأ حمراء")
    print("   - الرسالة تقول 'الرقم الوطني لا يتطابق'")
    print("   - لا يظهر نموذج تقديم الطلب")
    
    print("\n" + "=" * 50)
    print("🔍 رسائل الكونسول المتوقعة:")
    print("=" * 50)
    print("✅ للموظف العادي:")
    print("   - 🔍 SEARCH BUTTON CLICKED!")
    print("   - 📡 Response status: 200")
    print("   - ✅ Employee found successfully")
    print("   - 📝 FILLING EMPLOYEE DATA")
    print("   - ✅ Employee section shown")
    print("\n❌ لمعلم BTEC:")
    print("   - 🔍 SEARCH BUTTON CLICKED!")
    print("   - 📡 Response status: 200")
    print("   - ❌ Search failed: [رسالة BTEC]")
    print("   - 🚨 SHOWING ERROR: [رسالة BTEC]")
    print("   - ✅ Error message shown in UI")
    
    print("\n" + "=" * 50)
    print("🎉 جاهز للاختبار الشامل!")
    print("=" * 50)

if __name__ == "__main__":
    create_final_test_data()
