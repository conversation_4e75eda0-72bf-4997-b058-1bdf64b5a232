from django.urls import path
from . import views

app_name = 'accounts'

urlpatterns = [
    path('', views.user_list, name='user_list'),
    path('add/', views.user_create, name='user_create'),
    path('<int:pk>/', views.user_detail, name='user_detail'),
    path('<int:pk>/edit/', views.user_update, name='user_update'),
    path('<int:pk>/delete/', views.user_delete, name='user_delete'),
    path('<int:pk>/change-password/', views.admin_change_password, name='admin_change_password'),
    path('<int:pk>/permissions/<int:permission_id>/pages/', views.user_visible_pages, name='user_visible_pages'),
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('profile/', views.profile_view, name='profile'),
    path('instructions/', views.instructions_view, name='instructions'),
    path('change-password/', views.change_password, name='change_password'),
    path('reload-permissions/', views.reload_permissions_view, name='reload_permissions'),
]
