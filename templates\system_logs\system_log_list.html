{% extends 'base.html' %}
{% load static %}
{% load system_log_tags %}

{% block title %}سجل حركات النظام - نظام شؤون الموظفين{% endblock %}

{% block extra_css %}
<style>
    .description-cell {
        font-size: 14px;
        line-height: 1.5;
        color: #333;
        background-color: #f8f9fa;
        padding: 10px 15px !important;
        border-radius: 4px;
    }

    .table td {
        vertical-align: middle;
        white-space: normal;
        word-wrap: break-word;
    }

    .table th {
        text-align: center;
        background-color: #f1f5f9;
    }

    /* تحسين عرض الجدول */
    .table-responsive {
        overflow-x: auto;
        min-height: 300px;
    }

    /* تأكيد أن الجدول يأخذ العرض الكامل */
    .table {
        width: 100% !important;
        table-layout: fixed;
    }

    /* تنسيق الأسماء في الوصف */
    .description-cell strong {
        color: #0d6efd;
        font-weight: bold;
    }

    /* تنسيق الأقواس في الوصف */
    .description-cell span.object-name {
        color: #198754;
        font-weight: 500;
    }

    /* تحسين مظهر الخلايا */
    .table-hover tbody tr:hover {
        background-color: rgba(13, 110, 253, 0.05);
    }

    /* تحسين مظهر الصفوف الفردية والزوجية */
    .table-hover tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.02);
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2>سجل حركات النظام</h2>
        <p class="text-muted">
            <i class="fas fa-clock me-1"></i> جميع التواريخ والأوقات تعرض حسب التوقيت المحلي (Asia/Amman - توقيت الأردن)
        </p>
    </div>
    <div>
        <a href="?export=1{% if selected_module %}&module={{ selected_module }}{% endif %}{% if selected_action %}&action={{ selected_action }}{% endif %}{% if selected_user %}&user={{ selected_user }}{% endif %}{% if selected_date_range %}&date_range={{ selected_date_range }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}" class="btn btn-success">
            <i class="fas fa-file-export"></i> تصدير إلى Excel
        </a>
        <form method="post" action="{% url 'system_logs:update_log_descriptions' %}" class="d-inline" id="updateDescriptionsForm">
            {% csrf_token %}
            <button type="submit" class="btn btn-primary mx-2" id="updateDescriptionsBtn" title="تحديث جميع أوصاف السجلات إلى اللغة العربية">
                <i class="fas fa-language"></i> تحديث الأوصاف بالعربية
            </button>
        </form>
        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#clearLogsModal">
            <i class="fas fa-trash"></i> مسح السجلات
        </button>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">تصفية السجلات</h6>
    </div>
    <div class="card-body">
        <form method="get" class="mb-4">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="module" class="form-label">القسم</label>
                    <select name="module" id="module" class="form-control">
                        <option value="">الكل</option>
                        {% for module in modules_data %}
                        <option value="{{ module.value }}" {% if selected_module == module.value %}selected{% endif %}>
                            {{ module.display }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="action" class="form-label">الإجراء</label>
                    <select name="action" id="action" class="form-control">
                        <option value="">الكل</option>
                        {% for action in actions_data %}
                        <option value="{{ action.value }}" {% if selected_action == action.value %}selected{% endif %}>
                            {{ action.display }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="user" class="form-label">المستخدم</label>
                    <select name="user" id="user" class="form-control">
                        <option value="">الكل</option>
                        {% for user in users_data %}
                        <option value="{{ user.id }}" {% if selected_user == user.id|stringformat:"i" %}selected{% endif %}>
                            {{ user.username }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="date_range" class="form-label">الفترة الزمنية</label>
                    <select name="date_range" id="date_range" class="form-control">
                        <option value="1" {% if selected_date_range == '1' %}selected{% endif %}>آخر 24 ساعة</option>
                        <option value="7" {% if selected_date_range == '7' %}selected{% endif %}>آخر 7 أيام</option>
                        <option value="30" {% if selected_date_range == '30' %}selected{% endif %}>آخر 30 يوم</option>
                        <option value="90" {% if selected_date_range == '90' %}selected{% endif %}>آخر 3 أشهر</option>
                        <option value="0" {% if selected_date_range == '0' %}selected{% endif %}>كل السجلات</option>
                    </select>
                </div>
            </div>
            <div class="row">
                <div class="col-md-9 mb-3">
                    <label for="search" class="form-label">بحث</label>
                    <input type="text" name="search" id="search" class="form-control" value="{{ search_query }}" placeholder="ابحث في الصفحة، الوصف، المستخدم...">
                </div>
                <div class="col-md-3 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-dark w-100">
                        <i class="fas fa-filter"></i> تصفية
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

{% if sidebar_pages and selected_module %}
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">صفحات قسم {% for module in modules_data %}{% if module.value == selected_module %}{{ module.display }}{% endif %}{% endfor %}</h6>
    </div>
    <div class="card-body">
        <div class="row">
            {% for page in sidebar_pages|get_item:selected_module %}
            <div class="col-md-4 mb-2">
                <div class="card">
                    <div class="card-body py-2 px-3 text-center">
                        <small class="d-block text-truncate">{{ page }}</small>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <p class="text-center">لا توجد صفحات لهذا القسم</p>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="m-0 font-weight-bold text-primary">سجلات حركات النظام</h6>
        </div>

        <div class="d-flex justify-content-center align-items-center flex-wrap">
            <div class="d-flex align-items-center mx-3 mb-2">
                <span class="badge bg-primary rounded-pill fs-5 me-2" style="font-size: 1.2rem; padding: 0.5rem 0.8rem;">
                    <i class="fas fa-list-ul me-1"></i>
                    <span id="total-logs-count">{{ logs.count }}</span>
                </span>
                <span class="fw-bold" style="font-size: 1.1rem;">إجمالي السجلات</span>
            </div>

            <div class="mx-3 mb-2">
                <div class="input-group">
                    <span class="input-group-text bg-light fw-bold">
                        <i class="fas fa-eye me-1"></i> عرض
                    </span>
                    <select class="form-select form-select-lg" id="limit-selector" style="min-width: 150px; font-size: 1rem;">
                        <option value="100" selected>100 سجل</option>
                        <option value="200">200 سجل</option>
                        <option value="500">500 سجل</option>
                        <option value="1000">1000 سجل</option>
                        <option value="0">عرض الكل</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover w-100">
                <thead class="table-light">
                    <tr>
                        <th style="min-width: 150px;"><i class="fas fa-calendar-alt me-1"></i> التاريخ والوقت</th>
                        <th style="min-width: 100px;"><i class="fas fa-user me-1"></i> المستخدم</th>
                        <th style="min-width: 120px;"><i class="fas fa-folder me-1"></i> القسم</th>
                        <th style="min-width: 120px;"><i class="fas fa-cogs me-1"></i> الإجراء</th>
                        <th style="min-width: 150px;"><i class="fas fa-file-alt me-1"></i> الصفحة</th>
                        <th style="min-width: 350px; width: 30%;"><i class="fas fa-info-circle me-1"></i> الوصف</th>
                        <th style="min-width: 150px;"><i class="fas fa-desktop me-1"></i> نظام التشغيل والعنوان</th>
                    </tr>
                </thead>
                <tbody id="logs-table-body">
                    {% include 'system_logs/logs_table_rows.html' %}
                </tbody>
            </table>
            <div id="loading-indicator" class="text-center d-none">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p>جاري تحميل المزيد من السجلات...</p>
            </div>
        </div>
    </div>
</div>

<!-- Clear Logs Modal -->
<div class="modal fade" id="clearLogsModal" tabindex="-1" aria-labelledby="clearLogsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="clearLogsModalLabel">تأكيد مسح السجلات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير!</strong> سيؤدي هذا الإجراء إلى حذف جميع سجلات حركات النظام بشكل نهائي ولا يمكن التراجع عنه.
                </div>
                <p>لتأكيد الحذف، يرجى كتابة <strong>DELETE_ALL</strong> في الحقل أدناه:</p>
                <form method="post" action="{% url 'system_logs:clear_system_logs' %}" id="clearLogsForm">
                    {% csrf_token %}
                    <div class="form-group mb-3">
                        <input type="text" class="form-control" id="confirmationInput" name="confirmation" placeholder="اكتب DELETE_ALL للتأكيد" required>
                    </div>
                    <div class="d-flex justify-content-end">
                        <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash-alt me-1"></i> مسح السجلات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% block extra_js %}
<script>
    $(document).ready(function() {
        // Handle clear logs confirmation
        $('#clearLogsForm').on('submit', function(e) {
            const confirmation = $('#confirmationInput').val();
            if (confirmation !== 'DELETE_ALL') {
                e.preventDefault();
                alert('يرجى كتابة "DELETE_ALL" بالضبط للتأكيد على حذف جميع السجلات.');
                return false;
            }
            return true;
        });

        // Reset confirmation input when modal is closed
        $('#clearLogsModal').on('hidden.bs.modal', function() {
            $('#confirmationInput').val('');
        });

        // Current limit for logs
        let currentLimit = 100;

        // Function to load logs via AJAX
        function loadLogs() {
            // Show loading indicator
            $('#loading-indicator').removeClass('d-none');

            // Get current filter values
            const module = $('#module').val();
            const action = $('#action').val();
            const user = $('#user').val();
            const dateRange = $('#date_range').val();
            const search = $('#search').val();

            // Make AJAX request
            $.ajax({
                url: '{% url "system_logs:get_system_logs_ajax" %}',
                data: {
                    module: module,
                    action: action,
                    user: user,
                    date_range: dateRange,
                    search: search,
                    limit: currentLimit
                },
                success: function(response) {
                    // Update table body
                    $('#logs-table-body').html(response.html);

                    // Update total count
                    $('#total-logs-count').text(response.total_count);

                    // Hide loading indicator
                    $('#loading-indicator').addClass('d-none');
                },
                error: function() {
                    // Hide loading indicator
                    $('#loading-indicator').addClass('d-none');

                    // Show error message
                    alert('حدث خطأ أثناء تحميل السجلات. يرجى المحاولة مرة أخرى.');
                }
            });
        }

        // Handle limit selector change
        $('#limit-selector').on('change', function() {
            // Get new limit
            const newLimit = parseInt($(this).val());

            // Update current limit
            currentLimit = newLimit;

            // Load logs with new limit
            loadLogs();
        });

        // Handle update descriptions form
        $('#updateDescriptionsForm').on('submit', function(e) {
            e.preventDefault();

            const $btn = $('#updateDescriptionsBtn');
            const originalText = $btn.html();

            // Show loading state
            $btn.prop('disabled', true);
            $btn.html('<i class="fas fa-spinner fa-spin"></i> جاري التحديث...');

            // Submit form via AJAX
            $.ajax({
                url: $(this).attr('action'),
                method: 'POST',
                data: $(this).serialize(),
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message with details
                        const successMessage = `✅ تم تحديث الأوصاف بنجاح!\n\n` +
                                              `📊 الإحصائيات:\n` +
                                              `• تم تحديث: ${response.updated_count} سجل\n` +
                                              `• إجمالي السجلات: ${response.total_logs}\n\n` +
                                              `🔄 سيتم إعادة تحميل السجلات لعرض التحديثات...`;

                        alert(successMessage);

                        // Reload the logs to show updated descriptions
                        loadLogs();
                    } else {
                        alert(response.message || 'حدث خطأ أثناء تحديث الأوصاف.');
                    }
                },
                error: function(xhr, status, error) {
                    let errorMessage = 'حدث خطأ أثناء تحديث الأوصاف. يرجى المحاولة مرة أخرى.';

                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }

                    alert(errorMessage);
                    console.error('Error:', error);
                },
                complete: function() {
                    // Restore button state
                    $btn.prop('disabled', false);
                    $btn.html(originalText);
                }
            });
        });

        // Handle filter form submission (but not the update descriptions form)
        $('form:not([action*="update_log_descriptions"]):not([action*="clear_system_logs"])').on('submit', function(e) {
            // Don't reload the page
            e.preventDefault();

            // Load logs with new filters
            loadLogs();
        });
    });
</script>
{% endblock %}
{% endblock %}