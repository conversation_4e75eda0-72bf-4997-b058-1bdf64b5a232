from django.urls import path
from . import views
from . import views_unpaid_leave
from . import views_directorate

app_name = 'reports'

urlpatterns = [
    path('', views.report_dashboard, name='report_dashboard'),
    path('attendance/', views.attendance_report, name='attendance_report'),
    path('employment/', views.employment_report, name='employment_report'),
    path('employee/', views.employee_selection, name='employee_selection'),
    path('employee/<int:pk>/', views.employee_report, name='employee_report'),
    path('department/', views.department_selection, name='department_selection'),
    path('department/<int:pk>/', views.department_report, name='department_report'),
    path('leaves/', views.leave_report, name='leave_report'),
    path('unpaid-leaves/', views_unpaid_leave.unpaid_leave_report, name='unpaid_leave_report'),
    path('staff/', views.staff_report, name='staff_report'),
    path('technical-position/', views.technical_position_report, name='technical_position_report'),
    path('position/', views.position_report, name='position_report'),
    path('specialization/', views.specialization_report, name='specialization_report'),
    path('directorate-employees/', views_directorate.directorate_employees_report, name='directorate_employees_report'),
    path('directorate-employees/whatsapp/<int:employee_id>/', views_directorate.get_employee_whatsapp_data, name='get_employee_whatsapp_data'),
    path('directorate-employees/whatsapp/department/', views_directorate.get_employee_whatsapp_data, {'employee_id': '0'}, name='get_department_whatsapp_data'),
    path('list/', views.report_list, name='report_list'),
    path('<int:pk>/delete/', views.report_delete, name='report_delete'),
]
