@echo off
echo This script will install all required Python packages.
echo.

REM Check if venv exists
if exist "venv\Scripts\python.exe" (
    echo Using Python from venv\Scripts\python.exe
    venv\Scripts\python.exe -m pip install -r requirements.txt
    goto :end
)

REM Check if env exists
if exist "env\Scripts\python.exe" (
    echo Using Python from env\Scripts\python.exe
    env\Scripts\python.exe -m pip install -r requirements.txt
    goto :end
)

REM Check common Python installation paths
if exist "C:\Python311\python.exe" (
    echo Using Python from C:\Python311\python.exe
    C:\Python311\python.exe -m pip install -r requirements.txt
    goto :end
)

if exist "C:\Python310\python.exe" (
    echo Using Python from C:\Python310\python.exe
    C:\Python310\python.exe -m pip install -r requirements.txt
    goto :end
)

if exist "C:\Python39\python.exe" (
    echo Using Python from C:\Python39\python.exe
    C:\Python39\python.exe -m pip install -r requirements.txt
    goto :end
)

if exist "C:\Program Files\Python311\python.exe" (
    echo Using Python from C:\Program Files\Python311\python.exe
    "C:\Program Files\Python311\python.exe" -m pip install -r requirements.txt
    goto :end
)

if exist "C:\Program Files\Python310\python.exe" (
    echo Using Python from C:\Program Files\Python310\python.exe
    "C:\Program Files\Python310\python.exe" -m pip install -r requirements.txt
    goto :end
)

if exist "C:\Program Files\Python39\python.exe" (
    echo Using Python from C:\Program Files\Python39\python.exe
    "C:\Program Files\Python39\python.exe" -m pip install -r requirements.txt
    goto :end
)

if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" (
    echo Using Python from C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe" -m pip install -r requirements.txt
    goto :end
)

if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" (
    echo Using Python from C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" -m pip install -r requirements.txt
    goto :end
)

if exist "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe" (
    echo Using Python from C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe" -m pip install -r requirements.txt
    goto :end
)

echo Python not found in common locations.
echo Please install Python first, then run this script again.
echo.
echo Press any key to exit...
pause > nul

:end
echo.
echo Installation completed. Press any key to exit...
pause > nul
