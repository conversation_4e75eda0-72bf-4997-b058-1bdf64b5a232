#!/usr/bin/env python
"""
Test BTEC teacher alert functionality
"""

import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hr_system.settings')
django.setup()

from django.test import Client
from employees.models import Employee
from employment.models import B<PERSON>Field, BtecTeacher, EmployeeIdentification
from datetime import date
import json

def test_btec_alert():
    """Test BTEC teacher alert functionality"""
    
    print("🧪 Testing BTEC Teacher Alert")
    print("=" * 40)
    
    # Clean up existing test data
    Employee.objects.filter(ministry_number="ALERTTEST").delete()
    BtecField.objects.filter(name="Alert Test Field").delete()
    
    # Create BTEC teacher
    btec_employee = Employee.objects.create(
        ministry_number="ALERTTEST",
        full_name="معلم BTEC للاختبار",
        national_id="ALERTTEST123",
        gender="male",
        qualification="ماجستير",
        specialization="تقنية معلومات",
        school="مدرسة الاختبار",
        hire_date=date(2021, 1, 1),
        birth_date=date(1985, 5, 15),
        address="عنوان تجريبي",
        phone_number="0987654321"
    )
    
    # Create identification
    btec_identification = EmployeeIdentification.objects.create(
        employee=btec_employee,
        ministry_number="ALERTTEST",
        national_id="ALERTTEST123",
        id_number="ALERTTEST456",
        birth_day=15,
        birth_month=5,
        birth_year=1985,
        address="عنوان تجريبي"
    )
    
    # Create BTEC field and assign teacher
    btec_field = BtecField.objects.create(
        name="Alert Test Field",
        description="Test field for alert"
    )
    
    btec_teacher = BtecTeacher.objects.create(
        employee=btec_employee,
        field=btec_field
    )
    
    print(f"✅ Created BTEC teacher: {btec_employee.full_name}")
    print(f"   الرقم الوزاري: {btec_employee.ministry_number}")
    print(f"   الرقم الوطني: {btec_employee.national_id}")
    print(f"   رقم الهوية: {btec_identification.id_number}")
    print(f"   حقل BTEC: {btec_field.name}")
    
    # Test the search endpoint
    client = Client()
    
    print("\n🔍 Testing search endpoint...")
    response = client.get('/internal-transfer/search/', {
        'ministry_number': 'ALERTTEST',
        'national_id': 'ALERTTEST123',
        'id_number': 'ALERTTEST456'
    })
    
    print(f"Response status: {response.status_code}")
    
    if response.status_code == 200:
        data = json.loads(response.content)
        print(f"Response data: {data}")
        
        if not data.get('success'):
            print(f"✅ BTEC teacher correctly blocked")
            print(f"Error message: {data.get('error')}")
            
            # Check if the error message mentions BTEC
            if 'BTEC' in data.get('error', ''):
                print("✅ Error message correctly mentions BTEC")
            else:
                print("❌ Error message doesn't mention BTEC")
        else:
            print("❌ BTEC teacher was NOT blocked - this is the problem!")
    else:
        print(f"❌ Unexpected response status: {response.status_code}")
    
    # Test the page load to see if JavaScript is working
    print("\n📄 Testing page load...")
    page_response = client.get('/internal-transfer/')
    print(f"Page status: {page_response.status_code}")
    
    if page_response.status_code == 200:
        content = page_response.content.decode('utf-8')
        
        # Check if JavaScript functions are present
        js_checks = [
            'initializeSearch',
            'showError',
            'fillEmployeeData',
            'Search button clicked'
        ]
        
        for check in js_checks:
            if check in content:
                print(f"✅ Found: {check}")
            else:
                print(f"❌ Missing: {check}")
    
    # Clean up
    print("\n🧹 Cleaning up...")
    btec_teacher.delete()
    btec_field.delete()
    btec_identification.delete()
    btec_employee.delete()
    print("✅ Cleanup complete")
    
    print("\n" + "=" * 40)
    print("🎯 Test Results Summary:")
    print("=" * 40)
    print("If the BTEC teacher was correctly blocked, the alert should work.")
    print("If not, there might be an issue with the JavaScript or the API response.")
    print("\nTo test manually:")
    print("1. Open: http://127.0.0.1:8000/internal-transfer/")
    print("2. Open browser console (F12)")
    print("3. Enter the BTEC teacher data above")
    print("4. Click search button")
    print("5. Check console for error messages")

if __name__ == "__main__":
    test_btec_alert()
