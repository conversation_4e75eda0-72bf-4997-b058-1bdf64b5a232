{% extends 'home/base.html' %}

{% block title %}حول النظام - نظام إدارة الموارد البشرية{% endblock %}

{% block extra_css %}
<style>
    /* Page Background */
    body {
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect x="0" y="0" width="100" height="100" fill="rgba(0,0,0,0.02)"/></svg>');
        background-attachment: fixed;
    }

    /* Enhanced Hero Section */
    .about-hero {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark), var(--secondary-color));
        background-size: 300% 300%;
        animation: gradientAnimation 15s ease infinite;
        padding: 5rem 0;
        margin-bottom: 4rem;
        position: relative;
        overflow: hidden;
        color: white;
        border-radius: 0 0 50% 50% / 20%;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }

    @keyframes gradientAnimation {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    .about-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect x="0" y="0" width="100" height="100" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="2"/><rect x="25" y="25" width="50" height="50" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/><circle cx="50" cy="50" r="20" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></svg>');
        opacity: 0.4;
        animation: patternMove 60s linear infinite;
    }

    @keyframes patternMove {
        0% { background-position: 0 0; }
        100% { background-position: 1000px 1000px; }
    }

    .about-hero .container {
        position: relative;
        z-index: 1;
    }

    /* Enhanced Logo Animation */
    .about-logo {
        width: 140px;
        height: 140px;
        background: linear-gradient(135deg, white, #f8f9fa);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2.5rem;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        position: relative;
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0% { transform: translateY(0px); }
        50% { transform: translateY(-15px); }
        100% { transform: translateY(0px); }
    }

    .about-logo::before {
        content: '';
        position: absolute;
        top: -10px;
        left: -10px;
        right: -10px;
        bottom: -10px;
        border-radius: 50%;
        background: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--primary-color));
        background-size: 200% 200%;
        animation: gradientAnimation 5s ease infinite;
        z-index: -1;
        opacity: 0.7;
    }

    .about-logo i {
        font-size: 3.5rem;
        color: var(--primary-color);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    /* Enhanced Section Titles */
    .section-title {
        position: relative;
        padding-bottom: 15px;
        margin-bottom: 30px;
        font-weight: 700;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        border-radius: 2px;
    }

    /* Enhanced Feature Cards */
    .feature-card {
        height: 100%;
        border-radius: 1rem;
        overflow: hidden;
        transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
        border: none;
        position: relative;
        z-index: 1;
        background-color: white;
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--primary-color));
        background-size: 200% 200%;
        animation: gradientAnimation 5s ease infinite;
        z-index: -1;
        border-radius: 1rem;
        opacity: 0;
        transition: opacity 0.5s ease;
    }

    .feature-card:hover::before {
        opacity: 1;
    }

    .feature-card::after {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        right: 2px;
        bottom: 2px;
        background-color: white;
        border-radius: calc(1rem - 2px);
        z-index: -1;
    }

    .feature-card:hover {
        transform: translateY(-15px) scale(1.03);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .feature-icon {
        width: 90px;
        height: 90px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        font-size: 2.2rem;
        box-shadow: 0 10px 25px rgba(var(--primary-color-rgb), 0.3);
        transition: all 0.5s ease;
        position: relative;
        overflow: hidden;
    }

    .feature-icon::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: rgba(255, 255, 255, 0.2);
        transform: rotate(45deg);
        animation: shimmer 3s linear infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) rotate(45deg); }
        100% { transform: translateX(100%) rotate(45deg); }
    }

    .feature-card:hover .feature-icon {
        transform: scale(1.1) rotate(10deg);
    }

    .feature-card .card-title {
        color: var(--primary-color);
        font-weight: 700;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .feature-card:hover .card-title {
        color: var(--secondary-color);
    }

    .feature-card .card-text {
        color: var(--gray-700);
        line-height: 1.6;
    }

    /* Enhanced Team Card */
    .team-card {
        border-radius: 1rem;
        overflow: hidden;
        transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
        border: none;
        position: relative;
        z-index: 1;
    }

    .team-card::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--primary-color));
        background-size: 200% 200%;
        animation: gradientAnimation 5s ease infinite;
        z-index: -1;
        border-radius: 1rem;
        opacity: 0;
        transition: opacity 0.5s ease;
    }

    .team-card:hover::before {
        opacity: 1;
    }

    .team-card::after {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        right: 2px;
        bottom: 2px;
        background-color: white;
        border-radius: calc(1rem - 2px);
        z-index: -1;
    }

    .team-card:hover {
        transform: translateY(-15px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .team-img {
        width: 160px;
        height: 160px;
        border-radius: 50%;
        object-fit: cover;
        margin: 0 auto;
        border: 5px solid white;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        transition: all 0.5s ease;
    }

    .team-card:hover .team-img {
        transform: scale(1.1);
        border-color: var(--primary-light);
    }

    /* Enhanced Social Icons */
    .social-icons a {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 45px;
        height: 45px;
        border-radius: 50%;
        background-color: var(--primary-light);
        color: var(--primary-dark);
        margin: 0 8px;
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        position: relative;
        overflow: hidden;
    }

    .social-icons a::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
        opacity: 0;
        transition: opacity 0.4s ease;
    }

    .social-icons a i {
        position: relative;
        z-index: 1;
        transition: all 0.4s ease;
    }

    .social-icons a:hover {
        transform: translateY(-5px) scale(1.1);
        box-shadow: 0 10px 20px rgba(var(--primary-color-rgb), 0.3);
    }

    .social-icons a:hover::before {
        opacity: 1;
    }

    .social-icons a:hover i {
        color: white;
        transform: scale(1.2);
    }

    /* Enhanced Version Badge */
    .version-badge {
        display: inline-block;
        padding: 0.6rem 2rem;
        border-radius: 2rem;
        background: linear-gradient(135deg, var(--success-color), var(--success-dark));
        color: white;
        font-weight: bold;
        box-shadow: 0 10px 25px rgba(var(--success-color-rgb), 0.3);
        position: relative;
        overflow: hidden;
    }

    .version-badge::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: rgba(255, 255, 255, 0.2);
        transform: rotate(45deg);
        animation: shimmer 3s linear infinite;
    }

    /* Enhanced Timeline */
    .timeline {
        position: relative;
        max-width: 1200px;
        margin: 0 auto;
    }

    .timeline::after {
        content: '';
        position: absolute;
        width: 6px;
        background: linear-gradient(to bottom, var(--primary-light), var(--primary-color), var(--primary-dark));
        top: 0;
        bottom: 0;
        right: 50%;
        margin-right: -3px;
        border-radius: 1rem;
    }

    .timeline-container {
        padding: 15px 40px;
        position: relative;
        background-color: inherit;
        width: 50%;
        animation: fadeInUp 1s ease-out both;
    }

    .timeline-container:nth-child(1) {
        animation-delay: 0.2s;
    }

    .timeline-container:nth-child(2) {
        animation-delay: 0.4s;
    }

    .timeline-container:nth-child(3) {
        animation-delay: 0.6s;
    }

    .timeline-container:nth-child(4) {
        animation-delay: 0.8s;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .timeline-container::after {
        content: '';
        position: absolute;
        width: 25px;
        height: 25px;
        right: -13px;
        background: linear-gradient(135deg, white, #f8f9fa);
        border: 4px solid var(--primary-color);
        top: 15px;
        border-radius: 50%;
        z-index: 1;
        box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.3);
        transition: all 0.3s ease;
    }

    .timeline-container:hover::after {
        transform: scale(1.2);
        background-color: var(--primary-light);
    }

    .timeline-left {
        left: 0;
    }

    .timeline-right {
        left: 50%;
    }

    .timeline-right::after {
        left: -12px;
    }

    .timeline-content {
        padding: 25px 35px;
        background-color: white;
        position: relative;
        border-radius: 1rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .timeline-content:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    }

    .timeline-content h3 {
        color: var(--primary-color);
        margin-top: 0;
        font-weight: 700;
    }

    /* Contact Section Enhancements */
    .contact-icon {
        transition: all 0.4s ease;
    }

    .contact-icon:hover {
        transform: scale(1.1) rotate(10deg);
    }

    /* Responsive Adjustments */
    @media screen and (max-width: 768px) {
        .timeline::after {
            right: 31px;
        }

        .timeline-container {
            width: 100%;
            padding-right: 70px;
            padding-left: 25px;
        }

        .timeline-right {
            left: 0%;
        }

        .timeline-container::after {
            right: 18px;
        }

        .timeline-right::after {
            left: auto;
        }

        .about-hero {
            border-radius: 0 0 25% 25% / 10%;
        }

        .developer-section {
            padding: 2rem 1rem;
            margin: 1rem 0;
            border: 2px solid #fcd34d;
        }

        .developer-name {
            font-size: 2.2rem;
        }

        .developer-title {
            font-size: 1.1rem;
        }

        .developer-org {
            font-size: 1.1rem;
        }

        .developer-dept {
            font-size: 1rem;
        }

        .developer-icon {
            font-size: 3rem;
        }

        .developer-badge {
            font-size: 1rem;
            padding: 0.6rem 1.5rem;
        }
    }

    /* Animation for text elements */
    .animate-text {
        animation: fadeInUp 1s ease-out both;
    }

    .delay-1 {
        animation-delay: 0.2s;
    }

    .delay-2 {
        animation-delay: 0.4s;
    }

    .delay-3 {
        animation-delay: 0.6s;
    }

    /* Animated background for alert */
    .alert-info {
        background: linear-gradient(135deg, var(--info-light), var(--info-color));
        background-size: 200% 200%;
        animation: gradientAnimation 5s ease infinite;
        color: white;
        border: none;
    }

    /* Developer Section */
    .developer-section {
        background: #1f2937;
        color: white;
        border-radius: 20px;
        padding: 3rem 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
        border: 3px solid #fbbf24;
        margin: 2rem 0;
    }

    .developer-section::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(251, 191, 36, 0.05) 0%, transparent 70%);
        animation: rotate 30s linear infinite;
    }

    .developer-section::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 40%, rgba(251, 191, 36, 0.03) 50%, transparent 60%);
        animation: shimmer 4s ease-in-out infinite;
    }

    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes shimmer {
        0%, 100% { opacity: 0; }
        50% { opacity: 1; }
    }

    .developer-section .content {
        position: relative;
        z-index: 2;
    }

    .developer-title {
        font-size: 1.3rem;
        color: #f9fafb;
        margin-bottom: 1rem;
        font-weight: 600;
        letter-spacing: 1px;
        text-transform: uppercase;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    }

    .developer-name {
        font-size: 3.2rem;
        font-weight: 900;
        margin-bottom: 0.5rem;
        color: #fcd34d;
        text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
        line-height: 1.2;
        filter: drop-shadow(0 0 15px rgba(252, 211, 77, 0.8));
    }

    .developer-org {
        font-size: 1.4rem;
        color: #f9fafb;
        margin-bottom: 0.5rem;
        font-weight: 700;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    }

    .developer-dept {
        font-size: 1.2rem;
        color: #f3f4f6;
        font-weight: 500;
        margin-bottom: 1.5rem;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
    }

    .developer-badge {
        display: inline-block;
        background: linear-gradient(45deg, #f59e0b, #d97706);
        color: white;
        padding: 0.8rem 2rem;
        border-radius: 30px;
        font-size: 1.1rem;
        font-weight: 700;
        margin-top: 1.5rem;
        box-shadow: 0 6px 20px rgba(245, 158, 11, 0.6);
        border: 2px solid #fcd34d;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .developer-icon {
        font-size: 4rem;
        color: #fcd34d;
        margin-bottom: 1rem;
        animation: pulse-gold 2s infinite;
        filter: drop-shadow(0 0 20px rgba(252, 211, 77, 1));
    }

    @keyframes pulse-gold {
        0%, 100% {
            transform: scale(1);
            filter: drop-shadow(0 0 10px rgba(252, 211, 77, 0.5));
        }
        50% {
            transform: scale(1.1);
            filter: drop-shadow(0 0 20px rgba(252, 211, 77, 0.8));
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section with Enhanced Animations -->
<section class="about-hero">
    <div class="container text-center">
        <div class="about-logo">
            <i class="fas fa-building"></i>
        </div>
        <h1 class="display-4 fw-bold mb-3 animate-text">نظام إدارة الموارد البشرية</h1>
        <p class="lead mb-4 animate-text delay-1">نظام متكامل لإدارة شؤون الموظفين والكادر البشري بكفاءة وفعالية</p>
        <div class="version-badge mb-4 animate-text delay-2">الإصدار 1.0.0</div>
    </div>
</section>

<div class="container">
    <!-- About System Section with Enhanced Animations -->
    <section class="mb-5">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="section-title mb-4 animate-text">نبذة عن النظام</h2>
                <p class="lead mb-4 animate-text delay-1">
                    تم تطوير نظام إدارة الموارد البشرية لتلبية احتياجات المؤسسات التعليمية في إدارة شؤون الموظفين بطريقة فعالة وسهلة.
                    يوفر النظام مجموعة متكاملة من الأدوات والميزات التي تساعد في تنظيم وإدارة الموارد البشرية بكفاءة عالية.
                </p>
                <!-- Developer Section -->
                <div class="developer-section animate-text delay-2">
                    <div class="content">
                        <div class="developer-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <div class="developer-title">تم تصميم وبرمجة هذا النظام من قبل</div>
                        <div class="developer-name">أحمد العمري</div>
                        <div class="developer-org">وزارة التربية والتعليم</div>
                        <div class="developer-dept">مديرية قصبة المفرق</div>
                        <div class="developer-badge">
                            <i class="fas fa-award me-2"></i>
                            مطور النظام
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section with Enhanced Animations -->
    <section class="mb-5">
        <h2 class="section-title text-center mb-5 animate-text">مميزات النظام</h2>
        <div class="row g-4">
            <div class="col-md-4 animate-text" style="animation-delay: 0.1s;">
                <div class="card feature-card shadow-sm h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="card-title">إدارة الموظفين</h3>
                        <p class="card-text">
                            إدارة كاملة لبيانات الموظفين، بما في ذلك المعلومات الشخصية والوظيفية والمؤهلات العلمية.
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 animate-text" style="animation-delay: 0.2s;">
                <div class="card feature-card shadow-sm h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <h3 class="card-title">النقل الداخلي</h3>
                        <p class="card-text">
                            إدارة طلبات النقل الداخلي للموظفين بين الأقسام والمدارس بطريقة سهلة وفعالة.
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 animate-text" style="animation-delay: 0.3s;">
                <div class="card feature-card shadow-sm h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <h3 class="card-title">إدارة الإجازات</h3>
                        <p class="card-text">
                            متابعة وإدارة إجازات الموظفين بمختلف أنواعها وحساب الرصيد المتبقي.
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 animate-text" style="animation-delay: 0.4s;">
                <div class="card feature-card shadow-sm h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <h3 class="card-title">إدارة الملفات</h3>
                        <p class="card-text">
                            تنظيم وأرشفة ملفات الموظفين وتتبع حركتها داخل المؤسسة.
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 animate-text" style="animation-delay: 0.5s;">
                <div class="card feature-card shadow-sm h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h3 class="card-title">التقارير والإحصائيات</h3>
                        <p class="card-text">
                            إنشاء تقارير وإحصائيات متنوعة عن الموظفين والإجازات والأداء.
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 animate-text" style="animation-delay: 0.6s;">
                <div class="card feature-card shadow-sm h-100">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon">
                            <i class="fas fa-medal"></i>
                        </div>
                        <h3 class="card-title">تقييم الأداء</h3>
                        <p class="card-text">
                            إدارة عملية تقييم أداء الموظفين ومتابعة تطورهم الوظيفي.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- Contact Section with Enhanced Animations -->
    <section class="mb-5">
        <h2 class="section-title text-center mb-5 animate-text">تواصل معنا</h2>
        <div class="row">
            <div class="col-lg-8 mx-auto animate-text" style="animation-delay: 0.2s;">
                <div class="card shadow-sm attractive-card">
                    <div class="card-body p-4 text-center">
                        <div class="mb-4">
                            <h3 class="mb-2"> أحمد العمري</h3>
                            <p class="text-muted mb-3">مطور النظام - مديرية قصبة المفرق</p>
                            <div class="social-icons">
                                <a href="#" target="_blank"><i class="fab fa-facebook-f"></i></a>
                                <a href="#" target="_blank"><i class="fab fa-twitter"></i></a>
                                <a href="#" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                                <a href="#" target="_blank"><i class="fab fa-github"></i></a>
                            </div>
                        </div>
                        <hr>
                        <div class="row g-3 mt-4">
                            <div class="col-md-4 animate-text" style="animation-delay: 0.3s;">
                                <div class="d-flex flex-column align-items-center">
                                    <div class="bg-light rounded-circle p-3 mb-2 contact-icon">
                                        <i class="fas fa-envelope text-primary fa-2x"></i>
                                    </div>
                                    <h5>البريد الإلكتروني</h5>
                                    <p class="mb-0"><EMAIL></p>
                                </div>
                            </div>
                            <div class="col-md-4 animate-text" style="animation-delay: 0.4s;">
                                <div class="d-flex flex-column align-items-center">
                                    <div class="bg-light rounded-circle p-3 mb-2 contact-icon">
                                        <i class="fas fa-phone text-primary fa-2x"></i>
                                    </div>
                                    <h5>الهاتف</h5>
                                    <p class="mb-0">+962 7X XXX XXXX</p>
                                </div>
                            </div>
                            <div class="col-md-4 animate-text" style="animation-delay: 0.5s;">
                                <div class="d-flex flex-column align-items-center">
                                    <div class="bg-light rounded-circle p-3 mb-2 contact-icon">
                                        <i class="fas fa-map-marker-alt text-primary fa-2x"></i>
                                    </div>
                                    <h5>العنوان</h5>
                                    <p class="mb-0">مديرية قصبة المفرق</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
{% endblock %}
