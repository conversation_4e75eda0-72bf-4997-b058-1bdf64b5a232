{% extends 'base.html' %}
{% load static %}

{% block title %}أرصدة الإجازات - نظام شؤون الموظفين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>أرصدة الإجازات</h2>
    <div>
        <a href="{% url 'leaves:leave_balance_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة رصيد جديد
        </a>
        <a href="{% url 'leaves:leave_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للإجازات
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">جميع أرصدة الإجازات</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead>
                    <tr class="text-center">
                        <th><i class="fas fa-user me-1"></i> الموظف</th>
                        <th><i class="fas fa-calendar-check me-1"></i> نوع الإجازة</th>
                        <th><i class="fas fa-calendar-alt me-1"></i> السنة</th>
                        <th><i class="fas fa-coins me-1"></i> الرصيد الأولي</th>
                        <th><i class="fas fa-wallet me-1"></i> الرصيد المتبقي</th>
                        <th><i class="fas fa-cogs me-1"></i> الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for balance in balances %}
                    <tr class="text-center">
                        <td>
                            <a href="{% url 'employees:employee_detail' balance.employee.pk %}">
                                {{ balance.employee.full_name }}
                            </a>
                        </td>
                        <td>{{ balance.leave_type.get_name_display }}</td>
                        <td>{{ balance.year }}</td>
                        <td>{{ balance.initial_balance }}</td>
                        <td>{{ balance.remaining_balance }}</td>
                        <td>
                            <a href="{% url 'leaves:leave_balance_update' balance.pk %}" class="btn btn-warning btn-sm">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <a href="{% url 'leaves:leave_balance_delete' balance.pk %}" class="btn btn-danger btn-sm">
                                <i class="fas fa-trash"></i> حذف
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center">لا يوجد أرصدة إجازات</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
