import os
import sys
import time
import subprocess
import tempfile
import json
from datetime import datetime
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth.decorators import login_required, user_passes_test
from django.conf import settings
from django.http import HttpResponse, FileResponse, JsonResponse
from django.utils.translation import gettext_lazy as _
from django.core.files import File
from django.views.decorators.csrf import csrf_exempt
from .models import Backup
from .forms import BackupForm, RestoreForm, UploadBackupForm
# from .folder_dialog import get_folder_path  # Not used anymore


def is_admin(user):
    """Check if user is an admin"""
    return user.is_superuser


@login_required
@user_passes_test(is_admin)
def backup_list(request):
    """View for listing all backups"""
    backups = Backup.objects.all()
    return render(request, 'backup/backup_list.html', {'backups': backups})


@login_required
@user_passes_test(is_admin)
def create_backup(request):
    """View for creating a new backup"""
    if request.method == 'POST':
        form = BackupForm(request.POST)
        if form.is_valid():
            # Get backup location from form
            backup_location = form.cleaned_data['backup_location']

            # Check if backup location exists
            if not os.path.exists(backup_location):
                try:
                    os.makedirs(backup_location)
                except Exception as e:
                    messages.error(request, _(f'خطأ في إنشاء مجلد النسخ الاحتياطية: {str(e)}'))
                    return render(request, 'backup/backup_form.html', {'form': form})

            # Get database settings
            db_settings = settings.DATABASES['default']
            db_name = db_settings['NAME']

            # Generate backup filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"backup_{timestamp}.sql"
            backup_file_path = os.path.join(backup_location, backup_filename)

            # Create backup command
            if db_settings['ENGINE'] == 'django.db.backends.sqlite3':
                # SQLite backup
                backup_command = f"sqlite3 {db_name} .dump > {backup_file_path}"
            else:
                # PostgreSQL backup
                backup_command = f"pg_dump -U {db_settings['USER']} -h {db_settings.get('HOST', 'localhost')} -p {db_settings.get('PORT', '5432')} {db_name} > {backup_file_path}"

            try:
                # Execute backup command
                subprocess.run(backup_command, shell=True, check=True)

                # Create backup object
                backup = form.save(commit=False)
                backup.created_by = request.user.username

                # Save backup file to database
                with open(backup_file_path, 'rb') as f:
                    backup.file.save(backup_filename, File(f))

                # Update file size
                backup.size = backup.file.size
                backup.save()

                messages.success(request, _(f'تم إنشاء النسخة الاحتياطية بنجاح. تم حفظ الملف في: {backup_file_path}'))

                # Create notification for backup completion
                try:
                    from notifications.utils import notify_backup_completed
                    notify_backup_completed()
                except ImportError:
                    pass  # Notifications app not available

                return redirect('backup:backup_list')
            except Exception as e:
                messages.error(request, _(f'خطأ في إنشاء النسخة الاحتياطية: {str(e)}'))
    else:
        # Generate default name with timestamp
        default_name = f"Backup {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        # Set default backup location
        default_location = os.path.join(os.path.dirname(settings.BASE_DIR), 'backups')
        form = BackupForm(initial={'name': default_name, 'backup_location': default_location})

    return render(request, 'backup/backup_form.html', {'form': form})


@login_required
@user_passes_test(is_admin)
def restore_backup(request):
    """View for restoring a backup"""
    if request.method == 'POST':
        form = RestoreForm(request.POST, request.FILES)
        if form.is_valid():
            restore_type = form.cleaned_data['restore_type']

            # Create a temporary file to store the backup
            with tempfile.NamedTemporaryFile(delete=False, suffix='.sql') as temp_file:
                temp_file_path = temp_file.name

            # Get backup file based on restore type
            if restore_type == 'from_list':
                backup = form.cleaned_data['backup']
                # Copy backup file to temporary file
                with open(backup.file.path, 'rb') as src_file:
                    with open(temp_file_path, 'wb') as dest_file:
                        dest_file.write(src_file.read())
            else:  # from_file
                backup_file = form.cleaned_data['backup_file']
                # Save uploaded file to temporary file
                with open(temp_file_path, 'wb') as dest_file:
                    for chunk in backup_file.chunks():
                        dest_file.write(chunk)

                # Create a backup record for the uploaded file
                try:
                    backup_record = Backup(
                        name=f"Uploaded backup {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                        description=_('تم رفعه من صفحة استعادة النسخ الاحتياطية'),
                        created_by=request.user.username
                    )
                    # Save the file to the backup record
                    with open(temp_file_path, 'rb') as f:
                        backup_record.file.save(backup_file.name, File(f))
                    backup_record.size = backup_record.file.size
                    backup_record.save()
                except Exception as e:
                    # Just log the error, don't stop the restore process
                    print(f"Error saving backup record: {str(e)}")

            # Get database settings
            db_settings = settings.DATABASES['default']
            db_name = db_settings['NAME']

            try:
                # Restore backup command
                if db_settings['ENGINE'] == 'django.db.backends.sqlite3':
                    # SQLite restore
                    # First, create a backup of the current database
                    current_backup = f"{db_name}.bak"
                    os.rename(db_name, current_backup)

                    # Create a new empty database
                    open(db_name, 'w').close()

                    # Restore from backup
                    restore_command = f"sqlite3 {db_name} < {temp_file_path}"
                    subprocess.run(restore_command, shell=True, check=True)
                else:
                    # PostgreSQL restore
                    restore_command = f"psql -U {db_settings['USER']} -h {db_settings.get('HOST', 'localhost')} -p {db_settings.get('PORT', '5432')} {db_name} < {temp_file_path}"
                    subprocess.run(restore_command, shell=True, check=True)

                messages.success(request, _('تمت استعادة النسخة الاحتياطية بنجاح. يرجى إعادة تشغيل التطبيق.'))
                return redirect('backup:backup_list')
            except Exception as e:
                messages.error(request, _(f'خطأ في استعادة النسخة الاحتياطية: {str(e)}'))
                # If SQLite and error occurred, restore the original database
                if db_settings['ENGINE'] == 'django.db.backends.sqlite3' and os.path.exists(current_backup):
                    os.remove(db_name)
                    os.rename(current_backup, db_name)
            finally:
                # Clean up temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                # Remove SQLite backup if it exists
                if 'current_backup' in locals() and db_settings['ENGINE'] == 'django.db.backends.sqlite3' and os.path.exists(current_backup):
                    os.remove(current_backup)
    else:
        form = RestoreForm()

    return render(request, 'backup/restore_form.html', {'form': form})


@login_required
@user_passes_test(is_admin)
def download_backup(request, pk):
    """View for downloading a backup file"""
    backup = get_object_or_404(Backup, pk=pk)
    response = FileResponse(open(backup.file.path, 'rb'))
    response['Content-Disposition'] = f'attachment; filename="{os.path.basename(backup.file.name)}"'
    return response


@login_required
@user_passes_test(is_admin)
def delete_backup(request, pk):
    """View for deleting a backup"""
    backup = get_object_or_404(Backup, pk=pk)
    if request.method == 'POST':
        backup.delete()
        messages.success(request, _('Backup deleted successfully.'))
        return redirect('backup:backup_list')
    return render(request, 'backup/backup_confirm_delete.html', {'backup': backup})


@login_required
@user_passes_test(is_admin)
def select_folder_dialog(request):
    """View for opening a folder selection dialog"""
    try:
        # Get the path to the folder_selector.py script
        script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'folder_selector.py')

        # Run the script as a separate process
        result = subprocess.run(
            [sys.executable, script_path],
            capture_output=True,
            text=True,
            check=True
        )

        # Get the folder path from the script output
        folder_path = result.stdout.strip()

        return JsonResponse({'success': True, 'folder_path': folder_path})
    except subprocess.CalledProcessError as e:
        return JsonResponse({'success': False, 'error': f'Process error: {e.stderr}'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@user_passes_test(is_admin)
def upload_backup(request):
    """View for uploading a backup file"""
    if request.method == 'POST':
        form = UploadBackupForm(request.POST, request.FILES)
        if form.is_valid():
            backup = form.save(commit=False)
            backup.created_by = request.user.username
            backup.size = backup.file.size
            backup.save()
            messages.success(request, _('Backup uploaded successfully.'))
            return redirect('backup:backup_list')
    else:
        form = UploadBackupForm()

    return render(request, 'backup/upload_form.html', {'form': form})
